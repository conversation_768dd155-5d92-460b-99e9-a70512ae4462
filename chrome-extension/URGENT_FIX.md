# 🚨 紧急修复指南

## ⚠️ 问题确认

您的页面仍然在使用有问题的原版 `injected.js` 脚本，导致：

1. ❌ `TypeError: Cannot read properties of undefined (reading 'call')`
2. ❌ 网络拦截不工作
3. ❌ 无缓存数据
4. ❌ 检核功能无法正常使用

## 🔧 立即执行修复

### 步骤1: 重新加载扩展
1. 打开 `chrome://extensions/`
2. 找到"图片检核管理扩展"
3. 点击"重新加载"按钮 🔄

### 步骤2: 强制刷新页面
1. 回到目标页面
2. 按 `Ctrl+Shift+R` (Windows) 或 `Cmd+Shift+R` (Mac) 强制刷新
3. 或者按 `F12` 打开开发者工具，右键刷新按钮选择"清空缓存并硬性重新加载"

### 步骤3: 验证修复
打开Console，应该看到：

✅ **正确的日志**：
```
[Content] 内容脚本已加载
[Content] 修复版注入脚本已加载
[Injected-Fixed] 修复版检核管理器开始加载
[Injected-Fixed] 检核管理器已初始化
```

❌ **不应该看到**：
```
[Injected] 检核管理器已初始化
TypeError: Cannot read properties of undefined
```

### 步骤4: 测试功能
在Console中运行：
```javascript
// 检查是否为修复版
console.log('修复版扩展:', !!window.inspectionManagerFixed);
console.log('调试工具:', !!window.debugFixed);

// 如果是修复版，测试功能
if (window.debugFixed) {
    debugFixed.checkStatus();
    debugFixed.simulateData();
    debugFixed.openModal();
}
```

## 🎯 预期结果

修复后应该看到：

1. ✅ Console中显示 `[Injected-Fixed]` 开头的日志
2. ✅ 无JavaScript错误
3. ✅ `window.debugFixed` 对象可用
4. ✅ 模拟数据功能正常
5. ✅ 检核界面可以打开

## 🔍 如果仍有问题

### 检查文件是否正确
在Console中运行：
```javascript
// 检查当前加载的脚本
console.log('当前脚本:', document.querySelector('script[src*="injected"]')?.src);
```

应该显示包含 `injected-fixed.js` 的路径。

### 手动验证修复
如果自动修复不工作，可以手动验证：

1. **检查content.js第67行**：
   应该是 `chrome.runtime.getURL('injected-fixed.js')`
   而不是 `chrome.runtime.getURL('injected.js')`

2. **检查manifest.json**：
   应该包含 `"injected-fixed.js"` 在 `web_accessible_resources` 中

## 📊 修复验证清单

- [ ] 扩展已重新加载
- [ ] 页面已强制刷新
- [ ] Console显示 `[Injected-Fixed]` 日志
- [ ] 无 `TypeError` 错误
- [ ] `window.debugFixed` 可用
- [ ] `debugFixed.checkStatus()` 正常工作
- [ ] `debugFixed.simulateData()` 可以生成数据
- [ ] `debugFixed.openModal()` 可以打开界面

## 🚀 修复后的下一步

一旦修复成功，立即执行：

```javascript
// 1. 检查状态
debugFixed.checkStatus()

// 2. 开始监控网络请求
debugFixed.monitorRequests()

// 3. 在页面上点击查询按钮，观察实际的API请求

// 4. 测试模拟数据
debugFixed.simulateData()
debugFixed.openModal()
```

## ⏰ 重要提醒

**必须先完成修复，才能继续后续的功能测试！**

如果修复后仍有问题，请提供：
1. Console中的完整错误信息
2. `debugFixed.checkStatus()` 的输出结果
3. Network标签页中的请求列表

---

**立即行动**：重新加载扩展 → 强制刷新页面 → 验证修复效果！
