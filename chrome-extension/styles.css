/* 图片检核管理扩展 - 样式文件 */

/* 全局样式重置 */
.inspection-extension * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, sans-serif;
}

/* 主容器样式 */
.inspection-modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: rgba(0, 0, 0, 0.95) !important;
    z-index: 999999 !important;
    display: flex !important;
    flex-direction: column !important;
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.inspection-modal.show {
    opacity: 1;
}

/* 工具栏样式 */
.inspection-toolbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 16px 24px !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    flex-shrink: 0 !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
    backdrop-filter: blur(10px) !important;
}

.inspection-toolbar h3 {
    margin: 0 !important;
    font-size: 20px !important;
    font-weight: 600 !important;
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
}

.inspection-toolbar-left {
    display: flex !important;
    gap: 16px !important;
    align-items: center !important;
}

.inspection-toolbar-right {
    display: flex !important;
    gap: 12px !important;
    align-items: center !important;
}

/* 按钮样式 */
.inspection-btn {
    padding: 8px 16px !important;
    border: none !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
    text-decoration: none !important;
    outline: none !important;
}

.inspection-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3) !important;
}

.inspection-btn:active {
    transform: translateY(0) !important;
}

.inspection-btn-primary {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.inspection-btn-primary:hover {
    background: rgba(255, 255, 255, 0.3) !important;
}

.inspection-btn-success {
    background: #10b981 !important;
    color: white !important;
}

.inspection-btn-success:hover {
    background: #059669 !important;
}

.inspection-btn-warning {
    background: #f59e0b !important;
    color: white !important;
}

.inspection-btn-warning:hover {
    background: #d97706 !important;
}

.inspection-btn-danger {
    background: #ef4444 !important;
    color: white !important;
}

.inspection-btn-danger:hover {
    background: #dc2626 !important;
}

.inspection-btn-secondary {
    background: #6b7280 !important;
    color: white !important;
}

.inspection-btn-secondary:hover {
    background: #4b5563 !important;
}

/* 筛选器样式 */
.inspection-filter {
    padding: 8px 12px !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 8px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    font-size: 14px !important;
    outline: none !important;
    backdrop-filter: blur(10px) !important;
}

.inspection-filter:focus {
    border-color: rgba(255, 255, 255, 0.6) !important;
    background: rgba(255, 255, 255, 0.2) !important;
}

.inspection-filter option {
    background: #374151 !important;
    color: white !important;
}

/* 图片容器样式 */
.inspection-container {
    flex: 1 !important;
    overflow-y: auto !important;
    padding: 24px !important;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
    display: grid !important;
    grid-template-columns: repeat(auto-fill, minmax(340px, 1fr)) !important;
    gap: 24px !important;
    align-content: start !important;
}

/* 图片卡片样式 */
.inspection-card {
    background: white !important;
    border-radius: 16px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    border: 2px solid transparent !important;
    position: relative !important;
}

.inspection-card:hover {
    transform: translateY(-4px) !important;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
    border-color: #667eea !important;
}

.inspection-card.flagged {
    border-color: #ef4444 !important;
    box-shadow: 0 4px 20px rgba(239, 68, 68, 0.2) !important;
}

.inspection-card.approved {
    border-color: #10b981 !important;
    box-shadow: 0 4px 20px rgba(16, 185, 129, 0.2) !important;
}

/* 图片容器 */
.inspection-image-container {
    position: relative !important;
    overflow: hidden !important;
    height: 240px !important;
}

.inspection-image {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    cursor: pointer !important;
    transition: transform 0.3s ease !important;
    background: linear-gradient(45deg, #f3f4f6 25%, transparent 25%), 
                linear-gradient(-45deg, #f3f4f6 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #f3f4f6 75%), 
                linear-gradient(-45deg, transparent 75%, #f3f4f6 75%) !important;
    background-size: 20px 20px !important;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px !important;
}

.inspection-image:hover {
    transform: scale(1.05) !important;
}

/* 状态标签 */
.inspection-status-badge {
    position: absolute !important;
    top: 12px !important;
    right: 12px !important;
    padding: 6px 12px !important;
    border-radius: 20px !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    color: white !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

.inspection-status-badge.approved {
    background: rgba(16, 185, 129, 0.9) !important;
}

.inspection-status-badge.rejected {
    background: rgba(239, 68, 68, 0.9) !important;
}

.inspection-status-badge.pending {
    background: rgba(107, 114, 128, 0.9) !important;
}

/* AI标签 */
.inspection-ai-badge {
    position: absolute !important;
    top: 12px !important;
    left: 12px !important;
    padding: 4px 8px !important;
    border-radius: 12px !important;
    font-size: 11px !important;
    font-weight: 600 !important;
    color: white !important;
    backdrop-filter: blur(10px) !important;
}

.inspection-ai-badge.high-confidence {
    background: rgba(16, 185, 129, 0.9) !important;
}

.inspection-ai-badge.low-confidence {
    background: rgba(239, 68, 68, 0.9) !important;
}

/* 卡片内容 */
.inspection-card-content {
    padding: 20px !important;
}

.inspection-card-title {
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
    margin-bottom: 8px !important;
    line-height: 1.4 !important;
}

.inspection-card-subtitle {
    font-size: 14px !important;
    color: #6b7280 !important;
    margin-bottom: 16px !important;
    line-height: 1.4 !important;
}

.inspection-card-footer {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    gap: 12px !important;
}

.inspection-card-status {
    font-size: 13px !important;
    font-weight: 600 !important;
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
}

.inspection-card-actions {
    display: flex !important;
    gap: 8px !important;
}

.inspection-card-btn {
    padding: 6px 12px !important;
    border: none !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    color: white !important;
}

.inspection-card-btn.ai {
    background: #06b6d4 !important;
}

.inspection-card-btn.ai:hover {
    background: #0891b2 !important;
}

.inspection-card-btn.update {
    background: #3b82f6 !important;
}

.inspection-card-btn.update:hover {
    background: #2563eb !important;
}

/* 懒加载占位符 */
.inspection-image-placeholder {
    width: 100% !important;
    height: 240px !important;
    background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%) !important;
    background-size: 200% 100% !important;
    animation: loading 1.5s infinite !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: #9ca3af !important;
    font-size: 14px !important;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .inspection-container {
        grid-template-columns: 1fr !important;
        padding: 16px !important;
        gap: 16px !important;
    }
    
    .inspection-toolbar {
        padding: 12px 16px !important;
        flex-direction: column !important;
        gap: 12px !important;
    }
    
    .inspection-toolbar-left,
    .inspection-toolbar-right {
        width: 100% !important;
        justify-content: center !important;
    }
    
    .inspection-card {
        margin: 0 !important;
    }
}

/* 滚动条样式 */
.inspection-container::-webkit-scrollbar {
    width: 8px !important;
}

.inspection-container::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1) !important;
    border-radius: 4px !important;
}

.inspection-container::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3) !important;
    border-radius: 4px !important;
}

.inspection-container::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5) !important;
}
