// 配置页面JavaScript逻辑
class OptionsManager {
    constructor() {
        this.defaultConfig = {
            targetPages: ['assttest.dna-nev.com.cn'],
            buttonSelectors: ['.search-btn', '.query-btn'],
            queryEndpoint: '/ly/mp/busicen/acc/accCheckInfo/queryPage.do',
            updateEndpoint: '/ly/mp/busicen/acc/accCheckRecordInfo/updateById.do',
            requestTimeout: 30000,
            aiEnabled: false,
            aiApiUrl: '',
            aiApiKey: '',
            aiConfidenceThreshold: 60,
            cacheExpireTime: 30,
            maxCacheSize: 100,
            autoClearCache: true,
            debugMode: false,
            autoInject: true,
            injectionDelay: 1000,
            maxConcurrentRequests: 3
        };
        
        this.init();
    }
    
    async init() {
        this.setupTabs();
        this.setupEventListeners();
        await this.loadConfig();
        this.updateExtensionStatus();
        this.log('配置页面初始化完成');
    }
    
    setupTabs() {
        const tabs = document.querySelectorAll('.tab');
        const contents = document.querySelectorAll('.tab-content');
        
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const targetTab = tab.dataset.tab;
                
                // 移除所有活动状态
                tabs.forEach(t => t.classList.remove('active'));
                contents.forEach(c => c.classList.remove('active'));
                
                // 激活当前标签
                tab.classList.add('active');
                document.getElementById(targetTab).classList.add('active');
            });
        });
    }
    
    setupEventListeners() {
        // 监听输入变化
        const inputs = document.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('change', () => {
                this.markConfigChanged();
            });
        });
    }
    
    async loadConfig() {
        try {
            const result = await chrome.storage.sync.get('extensionConfig');
            const config = result.extensionConfig || this.defaultConfig;
            
            this.populateForm(config);
            this.showAlert('配置加载成功', 'success');
            this.log('配置加载完成', config);
        } catch (error) {
            this.showAlert('配置加载失败: ' + error.message, 'error');
            this.log('配置加载失败', error);
        }
    }
    
    populateForm(config) {
        // 基础配置
        this.renderList('target-pages-list', config.targetPages || [], 'removeTargetPage');
        this.renderList('button-selectors-list', config.buttonSelectors || [], 'removeButtonSelector');
        
        // 接口配置
        document.getElementById('query-endpoint').value = config.queryEndpoint || '';
        document.getElementById('update-endpoint').value = config.updateEndpoint || '';
        document.getElementById('request-timeout').value = config.requestTimeout || 30000;
        
        // AI配置
        document.getElementById('ai-enabled').checked = config.aiEnabled || false;
        document.getElementById('ai-api-url').value = config.aiApiUrl || '';
        document.getElementById('ai-api-key').value = config.aiApiKey || '';
        document.getElementById('ai-confidence-threshold').value = config.aiConfidenceThreshold || 60;
        
        // 缓存配置
        document.getElementById('cache-expire-time').value = config.cacheExpireTime || 30;
        document.getElementById('max-cache-size').value = config.maxCacheSize || 100;
        document.getElementById('auto-clear-cache').checked = config.autoClearCache !== false;
        
        // 高级配置
        document.getElementById('debug-mode').checked = config.debugMode || false;
        document.getElementById('auto-inject').checked = config.autoInject !== false;
        document.getElementById('injection-delay').value = config.injectionDelay || 1000;
        document.getElementById('max-concurrent-requests').value = config.maxConcurrentRequests || 3;
    }
    
    renderList(containerId, items, removeFunction) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';
        
        items.forEach((item, index) => {
            const listItem = document.createElement('div');
            listItem.className = 'list-item';
            listItem.innerHTML = `
                <span>${item}</span>
                <button class="remove-btn" onclick="${removeFunction}(${index})">删除</button>
            `;
            container.appendChild(listItem);
        });
    }
    
    async saveConfig() {
        try {
            const config = this.collectFormData();
            const validation = this.validateConfig(config);
            
            if (!validation.valid) {
                this.showAlert('配置验证失败: ' + validation.message, 'error');
                return;
            }
            
            await chrome.storage.sync.set({ extensionConfig: config });
            this.showAlert('配置保存成功', 'success');
            this.log('配置保存成功', config);
            
            // 通知后台脚本配置已更新
            chrome.runtime.sendMessage({ action: 'configUpdated', config });
            
        } catch (error) {
            this.showAlert('配置保存失败: ' + error.message, 'error');
            this.log('配置保存失败', error);
        }
    }
    
    collectFormData() {
        return {
            targetPages: this.getListItems('target-pages-list'),
            buttonSelectors: this.getListItems('button-selectors-list'),
            queryEndpoint: document.getElementById('query-endpoint').value,
            updateEndpoint: document.getElementById('update-endpoint').value,
            requestTimeout: parseInt(document.getElementById('request-timeout').value),
            aiEnabled: document.getElementById('ai-enabled').checked,
            aiApiUrl: document.getElementById('ai-api-url').value,
            aiApiKey: document.getElementById('ai-api-key').value,
            aiConfidenceThreshold: parseInt(document.getElementById('ai-confidence-threshold').value),
            cacheExpireTime: parseInt(document.getElementById('cache-expire-time').value),
            maxCacheSize: parseInt(document.getElementById('max-cache-size').value),
            autoClearCache: document.getElementById('auto-clear-cache').checked,
            debugMode: document.getElementById('debug-mode').checked,
            autoInject: document.getElementById('auto-inject').checked,
            injectionDelay: parseInt(document.getElementById('injection-delay').value),
            maxConcurrentRequests: parseInt(document.getElementById('max-concurrent-requests').value)
        };
    }
    
    getListItems(containerId) {
        const container = document.getElementById(containerId);
        const items = container.querySelectorAll('.list-item span');
        return Array.from(items).map(item => item.textContent);
    }
    
    validateConfig(config) {
        if (!config.targetPages || config.targetPages.length === 0) {
            return { valid: false, message: '至少需要配置一个目标页面' };
        }
        
        if (!config.queryEndpoint) {
            return { valid: false, message: '查询接口路径不能为空' };
        }
        
        if (!config.updateEndpoint) {
            return { valid: false, message: '更新接口路径不能为空' };
        }
        
        if (config.aiEnabled && !config.aiApiUrl) {
            return { valid: false, message: '启用AI功能时必须配置AI接口地址' };
        }
        
        if (config.aiEnabled && !config.aiApiKey) {
            return { valid: false, message: '启用AI功能时必须配置API密钥' };
        }
        
        if (config.requestTimeout < 1000 || config.requestTimeout > 60000) {
            return { valid: false, message: '请求超时时间必须在1-60秒之间' };
        }
        
        if (config.cacheExpireTime < 1 || config.cacheExpireTime > 1440) {
            return { valid: false, message: '缓存过期时间必须在1-1440分钟之间' };
        }
        
        return { valid: true };
    }
    
    async resetConfig() {
        if (confirm('确定要重置为默认配置吗？这将清除所有自定义设置。')) {
            this.populateForm(this.defaultConfig);
            await this.saveConfig();
            this.showAlert('配置已重置为默认值', 'info');
        }
    }
    
    async clearAllCache() {
        if (confirm('确定要清除所有缓存数据吗？此操作不可撤销。')) {
            try {
                await chrome.storage.local.clear();
                this.showAlert('所有缓存已清除', 'success');
                this.log('缓存清除完成');
            } catch (error) {
                this.showAlert('缓存清除失败: ' + error.message, 'error');
                this.log('缓存清除失败', error);
            }
        }
    }
    
    async testAIConnection() {
        const aiApiUrl = document.getElementById('ai-api-url').value;
        const aiApiKey = document.getElementById('ai-api-key').value;
        
        if (!aiApiUrl || !aiApiKey) {
            this.showAlert('请先配置AI接口地址和密钥', 'error');
            return;
        }
        
        try {
            const response = await fetch(aiApiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${aiApiKey}`
                },
                body: JSON.stringify({ test: true })
            });
            
            if (response.ok) {
                this.showAlert('AI连接测试成功', 'success');
            } else {
                this.showAlert(`AI连接测试失败: ${response.status} ${response.statusText}`, 'error');
            }
        } catch (error) {
            this.showAlert('AI连接测试失败: ' + error.message, 'error');
        }
    }
    
    exportConfig() {
        const config = this.collectFormData();
        const dataStr = JSON.stringify(config, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = 'inspection-extension-config.json';
        link.click();
        
        this.showAlert('配置已导出', 'success');
    }
    
    importConfig() {
        document.getElementById('config-file').click();
    }
    
    async handleConfigImport(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        try {
            const text = await file.text();
            const config = JSON.parse(text);
            
            const validation = this.validateConfig(config);
            if (!validation.valid) {
                this.showAlert('导入的配置无效: ' + validation.message, 'error');
                return;
            }
            
            this.populateForm(config);
            this.showAlert('配置导入成功，请点击保存按钮应用配置', 'success');
            
        } catch (error) {
            this.showAlert('配置导入失败: ' + error.message, 'error');
        }
    }
    
    updateExtensionStatus() {
        const statusIndicator = document.getElementById('extension-status');
        const statusText = document.getElementById('extension-status-text');
        const versionElement = document.getElementById('extension-version');
        const lastUpdatedElement = document.getElementById('last-updated');
        
        // 获取扩展信息
        const manifest = chrome.runtime.getManifest();
        versionElement.textContent = manifest.version;
        lastUpdatedElement.textContent = new Date().toLocaleString();
        
        // 检查扩展状态
        statusIndicator.className = 'status-indicator status-enabled';
        statusText.textContent = '扩展运行正常';
    }
    
    showAlert(message, type = 'info') {
        const container = document.getElementById('alert-container');
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.textContent = message;
        
        container.appendChild(alert);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 3000);
    }
    
    markConfigChanged() {
        // 可以在这里添加未保存更改的提示
    }
    
    log(message, data = null) {
        const timestamp = new Date().toISOString();
        console.log(`[Options ${timestamp}] ${message}`, data);
    }
}

// 全局函数，供HTML调用
let optionsManager;

// 目标页面管理
function addTargetPage() {
    const input = document.getElementById('new-target-page');
    const value = input.value.trim();
    
    if (!value) {
        optionsManager.showAlert('请输入有效的域名', 'error');
        return;
    }
    
    const container = document.getElementById('target-pages-list');
    const existingItems = optionsManager.getListItems('target-pages-list');
    
    if (existingItems.includes(value)) {
        optionsManager.showAlert('该域名已存在', 'error');
        return;
    }
    
    existingItems.push(value);
    optionsManager.renderList('target-pages-list', existingItems, 'removeTargetPage');
    input.value = '';
    optionsManager.markConfigChanged();
}

function removeTargetPage(index) {
    const existingItems = optionsManager.getListItems('target-pages-list');
    existingItems.splice(index, 1);
    optionsManager.renderList('target-pages-list', existingItems, 'removeTargetPage');
    optionsManager.markConfigChanged();
}

// 按钮选择器管理
function addButtonSelector() {
    const input = document.getElementById('new-button-selector');
    const value = input.value.trim();
    
    if (!value) {
        optionsManager.showAlert('请输入有效的CSS选择器', 'error');
        return;
    }
    
    const container = document.getElementById('button-selectors-list');
    const existingItems = optionsManager.getListItems('button-selectors-list');
    
    if (existingItems.includes(value)) {
        optionsManager.showAlert('该选择器已存在', 'error');
        return;
    }
    
    existingItems.push(value);
    optionsManager.renderList('button-selectors-list', existingItems, 'removeButtonSelector');
    input.value = '';
    optionsManager.markConfigChanged();
}

function removeButtonSelector(index) {
    const existingItems = optionsManager.getListItems('button-selectors-list');
    existingItems.splice(index, 1);
    optionsManager.renderList('button-selectors-list', existingItems, 'removeButtonSelector');
    optionsManager.markConfigChanged();
}

// 配置操作函数
function saveConfig() {
    optionsManager.saveConfig();
}

function resetConfig() {
    optionsManager.resetConfig();
}

function loadConfig() {
    optionsManager.loadConfig();
}

function clearAllCache() {
    optionsManager.clearAllCache();
}

function testAIConnection() {
    optionsManager.testAIConnection();
}

function exportConfig() {
    optionsManager.exportConfig();
}

function importConfig() {
    optionsManager.importConfig();
}

function handleConfigImport(event) {
    optionsManager.handleConfigImport(event);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    optionsManager = new OptionsManager();
});

// 页面卸载前检查未保存的更改
window.addEventListener('beforeunload', (event) => {
    // 这里可以检查是否有未保存的更改
    // 如果有，可以提示用户
});