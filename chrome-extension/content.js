// 内容脚本 - 页面交互和脚本注入
class ContentScript {
    constructor() {
        this.settings = null;
        this.isInjected = false;
        this.init();
    }

    async init() {
        console.log('[Content] 内容脚本已加载');
        
        // 获取扩展设置
        await this.loadSettings();
        
        // 检查当前页面是否需要注入
        if (this.shouldInject()) {
            await this.injectScript();
            this.setupMessageListener();
            this.startPageMonitoring();
        }
    }

    // 加载扩展设置
    async loadSettings() {
        try {
            const response = await chrome.runtime.sendMessage({ type: 'GET_SETTINGS' });
            if (response.success) {
                this.settings = response.data;
                console.log('[Content] 设置已加载:', this.settings);
            }
        } catch (error) {
            console.error('[Content] 加载设置失败:', error);
        }
    }

    // 判断是否应该在当前页面注入脚本
    shouldInject() {
        if (!this.settings || !this.settings.targetPages) {
            return false;
        }

        const currentHost = window.location.hostname;
        const currentPath = window.location.pathname;
        
        // 检查域名是否匹配
        const hostMatches = this.settings.targetPages.some(page => 
            currentHost.includes(page) || page.includes(currentHost)
        );

        if (hostMatches) {
            console.log('[Content] 当前页面匹配目标页面，准备注入脚本');
            return true;
        }

        return false;
    }

    // 注入脚本到页面
    async injectScript() {
        if (this.isInjected) {
            return;
        }

        try {
            // 创建并注入修复版脚本
            const script = document.createElement('script');
            script.src = chrome.runtime.getURL('injected-fixed.js');
            script.onload = () => {
                console.log('[Content] 修复版注入脚本已加载');
                this.isInjected = true;

                // 向注入的脚本发送配置
                window.postMessage({
                    type: 'EXTENSION_CONFIG',
                    data: this.settings
                }, '*');
            };
            script.onerror = (error) => {
                console.error('[Content] 修复版注入脚本加载失败:', error);
            };

            (document.head || document.documentElement).appendChild(script);
        } catch (error) {
            console.error('[Content] 注入脚本失败:', error);
        }
    }

    // 设置消息监听器
    setupMessageListener() {
        // 监听来自background的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleBackgroundMessage(request, sender, sendResponse);
            return true;
        });

        // 监听来自注入脚本的消息
        window.addEventListener('message', (event) => {
            if (event.source !== window) return;
            
            if (event.data.type && event.data.type.startsWith('INJECTED_')) {
                this.handleInjectedMessage(event.data);
            }
        });
    }

    // 处理来自background的消息
    async handleBackgroundMessage(request, sender, sendResponse) {
        console.log('[Content] 收到background消息:', request.type);

        try {
            switch (request.type) {
                case 'MAKE_API_REQUEST':
                    const result = await this.makeApiRequest(request.url, request.method, request.data);
                    sendResponse(result);
                    break;

                case 'RELOAD_SETTINGS':
                    await this.loadSettings();
                    sendResponse({ success: true });
                    break;

                default:
                    sendResponse({ success: false, error: '未知消息类型' });
            }
        } catch (error) {
            console.error('[Content] 处理background消息失败:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 处理来自注入脚本的消息
    async handleInjectedMessage(data) {
        console.log('[Content] 收到注入脚本消息:', data.type);

        try {
            switch (data.type) {
                case 'INJECTED_GET_CACHE':
                    const cache = await chrome.runtime.sendMessage({
                        type: 'GET_CACHE',
                        key: data.key
                    });
                    this.sendToInjected('CACHE_RESPONSE', {
                        requestId: data.requestId,
                        data: cache.data
                    });
                    break;

                case 'INJECTED_SET_CACHE':
                    await chrome.runtime.sendMessage({
                        type: 'SET_CACHE',
                        key: data.key,
                        data: data.data,
                        expireTime: data.expireTime
                    });
                    this.sendToInjected('CACHE_SET_RESPONSE', {
                        requestId: data.requestId,
                        success: true
                    });
                    break;

                case 'INJECTED_CLEAR_CACHE':
                    await chrome.runtime.sendMessage({
                        type: 'CLEAR_CACHE',
                        pattern: data.pattern
                    });
                    this.sendToInjected('CACHE_CLEAR_RESPONSE', {
                        requestId: data.requestId,
                        success: true
                    });
                    break;

                case 'INJECTED_UPDATE_STATUS':
                    const updateResult = await chrome.runtime.sendMessage({
                        type: 'UPDATE_INSPECTION_STATUS',
                        data: data.data
                    });
                    this.sendToInjected('UPDATE_STATUS_RESPONSE', {
                        requestId: data.requestId,
                        result: updateResult
                    });
                    break;

                case 'INJECTED_AI_INSPECTION':
                    const aiResult = await chrome.runtime.sendMessage({
                        type: 'AI_INSPECTION',
                        data: data.data
                    });
                    this.sendToInjected('AI_INSPECTION_RESPONSE', {
                        requestId: data.requestId,
                        result: aiResult
                    });
                    break;

                case 'INJECTED_LOG':
                    await chrome.runtime.sendMessage({
                        type: 'LOG',
                        level: data.level,
                        message: data.message,
                        data: data.data
                    });
                    break;

                case 'INJECTED_SHOW_MODAL':
                    this.showInspectionModal(data.data);
                    break;
            }
        } catch (error) {
            console.error('[Content] 处理注入脚本消息失败:', error);
        }
    }

    // 向注入脚本发送消息
    sendToInjected(type, data) {
        window.postMessage({
            type: `CONTENT_${type}`,
            ...data
        }, '*');
    }

    // 发起API请求
    async makeApiRequest(url, method = 'GET', data = null) {
        try {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                }
            };

            if (data && method !== 'GET') {
                options.body = JSON.stringify(data);
            }

            // 获取当前页面的cookies和headers
            const currentHeaders = this.getCurrentPageHeaders();
            Object.assign(options.headers, currentHeaders);

            console.log('[Content] 发起API请求:', url, options);

            const response = await fetch(url, options);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log('[Content] API请求成功:', result);
            
            return { success: true, data: result };
        } catch (error) {
            console.error('[Content] API请求失败:', error);
            return { success: false, error: error.message };
        }
    }

    // 获取当前页面的headers
    getCurrentPageHeaders() {
        const headers = {};
        
        // 尝试从页面中获取必要的headers
        try {
            // 获取Authorization token
            const authMeta = document.querySelector('meta[name="_token"]');
            if (authMeta) {
                headers['Authorization'] = authMeta.content;
            }

            // 从localStorage或sessionStorage获取token
            const token = localStorage.getItem('token') || sessionStorage.getItem('token');
            if (token) {
                headers['Authorization'] = token;
            }

            // 添加其他必要的headers
            headers['Accept'] = 'application/json, text/plain, */*';
            headers['Accept-Language'] = 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7';
            headers['X-Requested-With'] = 'XMLHttpRequest';
            
        } catch (error) {
            console.warn('[Content] 获取页面headers失败:', error);
        }

        return headers;
    }

    // 显示检核弹窗
    showInspectionModal(data) {
        // 创建弹窗容器
        const modal = document.createElement('div');
        modal.id = 'inspection-extension-modal';
        modal.className = 'inspection-modal-overlay';
        
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .inspection-modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(0, 0, 0, 0.8);
                z-index: 999999;
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                transition: opacity 0.3s ease;
            }
            
            .inspection-modal-overlay.show {
                opacity: 1;
            }
            
            .inspection-modal-content {
                background: white;
                border-radius: 8px;
                width: 95vw;
                height: 95vh;
                max-width: 1400px;
                max-height: 900px;
                display: flex;
                flex-direction: column;
                overflow: hidden;
                transform: scale(0.9);
                transition: transform 0.3s ease;
            }
            
            .inspection-modal-overlay.show .inspection-modal-content {
                transform: scale(1);
            }
            
            .inspection-modal-header {
                padding: 20px;
                border-bottom: 1px solid #eee;
                display: flex;
                justify-content: space-between;
                align-items: center;
                background: #f8f9fa;
            }
            
            .inspection-modal-body {
                flex: 1;
                padding: 20px;
                overflow-y: auto;
            }
            
            .inspection-close-btn {
                background: #dc3545;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
            }
            
            .inspection-close-btn:hover {
                background: #c82333;
            }
        `;
        
        document.head.appendChild(style);
        
        modal.innerHTML = `
            <div class="inspection-modal-content">
                <div class="inspection-modal-header">
                    <h3>检核图片管理</h3>
                    <button class="inspection-close-btn" onclick="this.closest('.inspection-modal-overlay').remove()">关闭</button>
                </div>
                <div class="inspection-modal-body">
                    <iframe src="${chrome.runtime.getURL('modal.html')}" 
                            style="width: 100%; height: 100%; border: none;"
                            id="inspection-modal-iframe"></iframe>
                </div>
            </div>
        `;
        
        // 点击遮罩关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
        
        document.body.appendChild(modal);
        
        // 显示动画
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
        
        // 向iframe传递数据
        setTimeout(() => {
            const iframe = modal.querySelector('#inspection-modal-iframe');
            if (iframe && iframe.contentWindow) {
                iframe.contentWindow.postMessage({
                    type: 'MODAL_DATA',
                    data: data
                }, '*');
            }
        }, 500);
    }

    // 开始页面监控
    startPageMonitoring() {
        // 监控DOM变化，确保按钮始终存在
        const observer = new MutationObserver((mutations) => {
            // 检查是否需要重新注入按钮
            this.checkAndInjectButton();
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // 初始检查
        setTimeout(() => {
            this.checkAndInjectButton();
        }, 2000);
    }

    // 检查并注入按钮
    checkAndInjectButton() {
        if (!this.settings || !this.settings.buttonSelectors) {
            return;
        }

        // 检查是否已经有扩展按钮
        if (document.querySelector('#inspection-extension-btn')) {
            return;
        }

        // 寻找目标按钮容器
        for (const selector of this.settings.buttonSelectors) {
            const container = document.querySelector(selector);
            if (container) {
                this.injectButton(container.parentElement || container);
                break;
            }
        }
    }

    // 注入扩展按钮
    injectButton(container) {
        const button = document.createElement('button');
        button.id = 'inspection-extension-btn';
        button.textContent = '📷 检核管理';
        button.style.cssText = `
            margin-left: 10px;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            z-index: 1000;
            position: relative;
        `;
        
        button.addEventListener('click', () => {
            this.sendToInjected('BUTTON_CLICKED', {});
        });
        
        container.appendChild(button);
        console.log('[Content] 扩展按钮已注入');
    }
}

// 初始化内容脚本
new ContentScript();