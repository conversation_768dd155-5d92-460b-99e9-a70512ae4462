<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复版扩展测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            background: #f8f9fa;
        }
        
        .test-section h3 {
            color: #495057;
            margin-bottom: 15px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .log-container {
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }
        
        .url-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 修复版扩展测试页面</h1>
            <p>测试 Illegal invocation 修复效果</p>
        </div>
        
        <div class="content">
            <!-- URL信息 -->
            <div class="url-info">
                <h3>📍 当前页面信息</h3>
                <p><strong>URL:</strong> <span id="current-url"></span></p>
                <p><strong>Hash:</strong> <span id="current-hash"></span></p>
                <p><strong>ActId:</strong> <span id="current-actid"></span></p>
            </div>
            
            <!-- 扩展状态检测 -->
            <div class="test-section">
                <h3>📊 扩展状态检测</h3>
                <div style="margin-bottom: 15px;">
                    <span class="status-indicator status-warning" id="extension-indicator"></span>
                    <span id="extension-status">检测中...</span>
                </div>
                <div style="margin-bottom: 15px;">
                    <span class="status-indicator status-warning" id="fixed-indicator"></span>
                    <span id="fixed-status">修复版检测中...</span>
                </div>
                <div>
                    <span class="status-indicator status-warning" id="intercept-indicator"></span>
                    <span id="intercept-status">网络拦截检测中...</span>
                </div>
            </div>
            
            <!-- 功能测试 -->
            <div class="test-section">
                <h3>🧪 功能测试</h3>
                <button class="btn" onclick="testExtensionStatus()">检查扩展状态</button>
                <button class="btn btn-secondary" onclick="testNetworkIntercept()">测试网络拦截</button>
                <button class="btn btn-success" onclick="simulateData()">模拟数据</button>
                <button class="btn" onclick="openModal()">打开检核界面</button>
                <button class="btn btn-danger" onclick="clearCache()">清除缓存</button>
            </div>
            
            <!-- 调试命令 -->
            <div class="test-section">
                <h3>🔧 调试命令</h3>
                <p>在浏览器控制台中运行以下命令：</p>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 12px;">
                    <div>// 检查修复版扩展状态</div>
                    <div style="color: #007bff;">debugFixed.checkStatus()</div>
                    <br>
                    <div>// 模拟API数据</div>
                    <div style="color: #007bff;">debugFixed.simulateData()</div>
                    <br>
                    <div>// 打开检核界面</div>
                    <div style="color: #007bff;">debugFixed.openModal()</div>
                    <br>
                    <div>// 清除缓存</div>
                    <div style="color: #007bff;">debugFixed.clearCache()</div>
                </div>
            </div>
            
            <!-- 操作日志 -->
            <div class="test-section">
                <h3>📝 操作日志</h3>
                <button class="btn btn-secondary" onclick="clearLog()">清除日志</button>
                <div class="log-container" id="log-container">
                    <div>[INFO] 测试页面已加载</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 测试页面JavaScript
        let logContainer = document.getElementById('log-container');
        
        // 日志函数
        function log(level, message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${level}] ${timestamp} - ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[TestPage] ${level}: ${message}`);
        }
        
        // 更新状态指示器
        function updateStatus(type, status, text) {
            const indicator = document.getElementById(`${type}-indicator`);
            const statusText = document.getElementById(`${type}-status`);
            
            indicator.className = `status-indicator status-${status}`;
            statusText.textContent = text;
        }
        
        // 检查扩展状态
        function checkExtensionStatus() {
            log('INFO', '开始检测扩展状态...');
            
            // 检测原版扩展
            if (window.inspectionManager) {
                updateStatus('extension', 'success', '原版扩展已加载');
                log('SUCCESS', '检测到原版扩展');
            } else {
                updateStatus('extension', 'error', '原版扩展未检测到');
                log('WARNING', '未检测到原版扩展');
            }
            
            // 检测修复版扩展
            if (window.inspectionManagerFixed) {
                updateStatus('fixed', 'success', '修复版扩展已加载');
                log('SUCCESS', '检测到修复版扩展');
            } else {
                updateStatus('fixed', 'error', '修复版扩展未检测到');
                log('ERROR', '未检测到修复版扩展');
            }
            
            // 检测网络拦截
            if (window.fetch !== window.originalFetch || window.inspectionManagerFixed) {
                updateStatus('intercept', 'success', '网络拦截已启用');
                log('SUCCESS', '网络拦截功能已启用');
            } else {
                updateStatus('intercept', 'warning', '网络拦截状态未知');
                log('WARNING', '网络拦截状态未知');
            }
        }
        
        // 测试扩展状态
        function testExtensionStatus() {
            log('INFO', '执行扩展状态测试...');
            
            if (window.debugFixed) {
                window.debugFixed.checkStatus();
                log('SUCCESS', '修复版调试工具可用');
            } else {
                log('ERROR', '修复版调试工具不可用');
            }
            
            checkExtensionStatus();
        }
        
        // 测试网络拦截
        function testNetworkIntercept() {
            log('INFO', '测试网络拦截功能...');
            
            // 模拟fetch请求
            fetch('/ly/mp/busicen/acc/accCheckInfo/queryPage.do', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ test: true })
            }).then(response => {
                log('SUCCESS', 'Fetch请求已发送');
            }).catch(error => {
                log('INFO', 'Fetch请求失败（预期行为）: ' + error.message);
            });
            
            // 模拟XHR请求
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/ly/mp/busicen/acc/accCheckInfo/queryPage.do');
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.onload = () => {
                log('SUCCESS', 'XHR请求完成');
            };
            xhr.onerror = () => {
                log('INFO', 'XHR请求失败（预期行为）');
            };
            xhr.send(JSON.stringify({ test: true }));
        }
        
        // 模拟数据
        function simulateData() {
            log('INFO', '模拟API数据...');
            
            if (window.debugFixed) {
                window.debugFixed.simulateData();
                log('SUCCESS', '模拟数据已生成');
            } else {
                log('ERROR', '修复版扩展不可用');
            }
        }
        
        // 打开检核界面
        function openModal() {
            log('INFO', '尝试打开检核界面...');
            
            if (window.debugFixed) {
                window.debugFixed.openModal();
                log('SUCCESS', '检核界面打开命令已发送');
            } else {
                log('ERROR', '修复版扩展不可用');
            }
        }
        
        // 清除缓存
        function clearCache() {
            log('INFO', '清除缓存...');
            
            if (window.debugFixed) {
                window.debugFixed.clearCache();
                log('SUCCESS', '缓存清除命令已发送');
            } else {
                log('ERROR', '修复版扩展不可用');
            }
        }
        
        // 清除日志
        function clearLog() {
            logContainer.innerHTML = '';
            log('INFO', '日志已清除');
        }
        
        // 更新页面信息
        function updatePageInfo() {
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('current-hash').textContent = window.location.hash || '(无)';
            
            // 提取actId
            const urlParams = new URLSearchParams(window.location.search);
            const hashParams = new URLSearchParams(window.location.hash.split('?')[1] || '');
            const actId = urlParams.get('actId') || hashParams.get('actId') || '(无)';
            document.getElementById('current-actid').textContent = actId;
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('INFO', '测试页面初始化完成');
            updatePageInfo();
            
            // 延迟检测扩展状态
            setTimeout(checkExtensionStatus, 1000);
            
            // 定期检测
            setInterval(checkExtensionStatus, 5000);
        });
        
        // 监听hash变化
        window.addEventListener('hashchange', updatePageInfo);
    </script>
</body>
</html>
