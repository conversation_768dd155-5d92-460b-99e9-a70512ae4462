# 图片检核管理扩展 - 快速开始指南

## 🚀 快速安装

### 1. 安装扩展
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `chrome-extension` 文件夹

### 2. 验证安装
- 扩展列表中应显示"图片检核管理扩展"
- 浏览器工具栏出现扩展图标 🖼️

## ⚙️ 基础配置

### 1. 打开配置页面
- 点击扩展图标 → "扩展设置"
- 或在扩展管理页面点击"选项"

### 2. 配置目标页面
```
目标页面域名：
assttest.dna-nev.com.cn
dms.dna-nev.com.cn
```

### 3. 配置接口路径
```
查询接口：/ly/mp/busicen/acc/accCheckInfo/queryPage.do
更新接口：/ly/mp/busicen/acc/accCheckRecordInfo/updateById.do
```

## 🧪 功能测试

### 1. 访问目标页面
打开以下任一页面：
- https://assttest.dna-nev.com.cn/#/veModule/marketFee/marketBasicData/activityAuditManagement
- https://assttest.dna-nev.com.cn/#/veModule/marketFee/marketBasicData/recordViewing?actId=xxx

### 2. 检查扩展状态
1. 打开浏览器开发者工具 (F12)
2. 切换到Console标签页
3. 查看是否有以下日志：
   ```
   [Content] 内容脚本已加载
   [Content] 当前页面匹配目标页面，准备注入脚本
   [Injected] 检核管理器已初始化
   ```

### 3. 使用调试工具
在Console中输入以下命令：
```javascript
// 检查扩展状态
extensionDebug.checkExtension()

// 模拟API数据
extensionDebug.simulateAPI()

// 打开检核界面
extensionDebug.openModal()

// 查看所有可用命令
extensionDebug.help()
```

## 🔧 故障排除

### 问题1: 扩展未加载
**症状**: Console中没有扩展相关日志
**解决方案**:
1. 检查扩展是否正确安装
2. 刷新页面重试
3. 检查页面URL是否匹配配置

### 问题2: "Illegal invocation" 错误
**症状**: Console中出现此错误
**解决方案**:
1. 已修复fetch和XHR拦截的this绑定问题
2. 重新加载扩展
3. 刷新页面

### 问题3: 按钮未出现
**症状**: 页面上没有"🖼️ 检核管理"按钮
**解决方案**:
1. 确认当前页面路径包含目标路径
2. 检查Console是否有错误信息
3. 使用 `extensionDebug.checkExtension()` 检查状态

### 问题4: API拦截不工作
**症状**: 点击查询按钮后没有数据
**解决方案**:
1. 检查接口路径配置是否正确
2. 使用 `extensionDebug.testIntercept()` 测试拦截
3. 查看Network标签页的请求

## 📝 使用流程

### 标准使用流程
1. **访问目标页面** - 打开活动审核或记录查看页面
2. **触发数据加载** - 点击页面上的查询按钮
3. **打开检核界面** - 点击"🖼️ 检核管理"按钮
4. **管理图片状态** - 查看、筛选、更新图片检核状态
5. **AI质检** (可选) - 使用AI功能进行自动质检

### 调试流程
1. **打开开发者工具** - 按F12打开Console
2. **检查扩展状态** - 运行 `extensionDebug.checkExtension()`
3. **模拟数据** - 运行 `extensionDebug.simulateAPI()`
4. **测试界面** - 运行 `extensionDebug.openModal()`

## 🎯 关键功能

### 1. 自动脚本注入
- 扩展会自动检测目标页面
- 在匹配的页面自动注入检核脚本
- 支持hash路由的SPA应用

### 2. API请求拦截
- 自动拦截查询接口响应
- 解析并缓存图片数据
- 支持fetch和XMLHttpRequest

### 3. 图片检核界面
- 网格布局显示图片
- 支持状态筛选和更新
- 懒加载优化性能

### 4. 缓存管理
- 按actId分组缓存数据
- 自动过期检查
- 手动清除和刷新

### 5. AI质检 (可选)
- 集成OpenAI API
- 可信度评分
- 批量处理支持

## 📊 数据结构

### 缓存数据格式
```javascript
{
  "data": {
    "result": "1",
    "rows": [
      {
        "actId": "活动ID",
        "actName": "活动名称",
        "items": [
          {
            "checkItemTitle": "检核项目",
            "rmarkLst": [
              {
                "markId": "标记ID",
                "markFlag": "1|2|0", // 1=合格, 2=不合格, 0=未点检
                "markReason": "原因",
                "checkItemValue": "图片URL"
              }
            ]
          }
        ]
      }
    ]
  },
  "timestamp": 1640995200000
}
```

### URL参数提取
- 从URL查询参数提取: `?actId=xxx`
- 从hash参数提取: `#/path?actId=xxx`
- 用于生成缓存键: `inspection_{actId}_{date}`

## 🔍 调试命令参考

```javascript
// 基础检查
extensionDebug.checkExtension()  // 检查扩展状态
extensionDebug.checkCache()      // 检查缓存数据
extensionDebug.help()            // 显示帮助

// 数据操作
extensionDebug.simulateAPI()     // 模拟API数据
extensionDebug.clearCache()      // 清除缓存

// 功能测试
extensionDebug.openModal()       // 打开检核界面
extensionDebug.testIntercept()   // 测试网络拦截
```

## 📞 获取支持

如遇问题，请：
1. 查看Console错误信息
2. 使用调试命令检查状态
3. 参考故障排除指南
4. 联系技术支持

---

**注意**: 确保在目标页面使用，并且已正确配置接口路径。
