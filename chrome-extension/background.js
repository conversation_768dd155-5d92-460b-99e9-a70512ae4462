// 后台脚本 - 处理扩展核心逻辑
class ExtensionBackground {
    constructor() {
        this.init();
    }

    init() {
        // 监听扩展安装
        chrome.runtime.onInstalled.addListener(() => {
            console.log('[Background] 扩展已安装');
            this.initDefaultSettings();
        });

        // 监听来自content script的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // 保持消息通道开放
        });

        // 监听网络请求
        this.setupNetworkInterceptor();
    }

    // 初始化默认设置
    async initDefaultSettings() {
        const defaultSettings = {
            targetPages: [
                'assttest.dna-nev.com.cn',
                'dms.dna-nev.com.cn'
            ],
            apiEndpoints: {
                query: '/ly/mp/busicen/acc/accCheckInfo/queryPage.do',
                update: '/ly/mp/busicen/acc/accCheckRecordInfo/updateById.do'
            },
            aiConfig: {
                enabled: false,
                apiUrl: '',
                apiKey: '',
                confidenceThreshold: 60
            },
            cacheConfig: {
                defaultExpireTime: 30 * 60 * 1000, // 30分钟
                maxCacheSize: 100 * 1024 * 1024 // 100MB
            },
            buttonSelectors: [
                '.u-btn-right .right-btn button.el-button--primary',
                'button:contains("查询")',
                'button[type="button"].el-button--primary',
                '#queryBtn',
                '.search-btn'
            ]
        };

        try {
            const existing = await chrome.storage.local.get('extensionSettings');
            if (!existing.extensionSettings) {
                await chrome.storage.local.set({ extensionSettings: defaultSettings });
                console.log('[Background] 默认设置已初始化');
            }
        } catch (error) {
            console.error('[Background] 初始化设置失败:', error);
        }
    }

    // 处理消息
    async handleMessage(request, sender, sendResponse) {
        console.log('[Background] 收到消息:', request.type);

        try {
            switch (request.type) {
                case 'GET_SETTINGS':
                    const settings = await this.getSettings();
                    sendResponse({ success: true, data: settings });
                    break;

                case 'UPDATE_SETTINGS':
                    await this.updateSettings(request.data);
                    sendResponse({ success: true });
                    break;

                case 'GET_CACHE':
                    const cache = await this.getCache(request.key);
                    sendResponse({ success: true, data: cache });
                    break;

                case 'SET_CACHE':
                    await this.setCache(request.key, request.data, request.expireTime);
                    sendResponse({ success: true });
                    break;

                case 'CLEAR_CACHE':
                    await this.clearCache(request.pattern);
                    sendResponse({ success: true });
                    break;

                case 'UPDATE_INSPECTION_STATUS':
                    const result = await this.updateInspectionStatus(request.data);
                    sendResponse(result);
                    break;

                case 'AI_INSPECTION':
                    const aiResult = await this.performAIInspection(request.data);
                    sendResponse(aiResult);
                    break;

                case 'LOG':
                    this.log(request.level, request.message, request.data);
                    sendResponse({ success: true });
                    break;

                default:
                    sendResponse({ success: false, error: '未知消息类型' });
            }
        } catch (error) {
            console.error('[Background] 处理消息失败:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // 获取设置
    async getSettings() {
        const result = await chrome.storage.local.get('extensionSettings');
        return result.extensionSettings || {};
    }

    // 更新设置
    async updateSettings(newSettings) {
        const current = await this.getSettings();
        const updated = { ...current, ...newSettings };
        await chrome.storage.local.set({ extensionSettings: updated });
        console.log('[Background] 设置已更新');
    }

    // 获取缓存
    async getCache(key) {
        try {
            const result = await chrome.storage.local.get(`cache_${key}`);
            const cacheData = result[`cache_${key}`];
            
            if (!cacheData) {
                return null;
            }

            // 检查是否过期
            if (Date.now() > cacheData.expireTime) {
                await chrome.storage.local.remove(`cache_${key}`);
                console.log(`[Background] 缓存已过期并清除: ${key}`);
                return null;
            }

            console.log(`[Background] 缓存命中: ${key}`);
            return cacheData.data;
        } catch (error) {
            console.error('[Background] 获取缓存失败:', error);
            return null;
        }
    }

    // 设置缓存
    async setCache(key, data, expireTime) {
        try {
            const settings = await this.getSettings();
            const defaultExpire = settings.cacheConfig?.defaultExpireTime || 30 * 60 * 1000;
            const expire = expireTime || defaultExpire;
            
            const cacheData = {
                data: data,
                expireTime: Date.now() + expire,
                createdTime: Date.now()
            };

            await chrome.storage.local.set({ [`cache_${key}`]: cacheData });
            console.log(`[Background] 缓存已设置: ${key}, 过期时间: ${new Date(cacheData.expireTime).toLocaleString()}`);
        } catch (error) {
            console.error('[Background] 设置缓存失败:', error);
        }
    }

    // 清除缓存
    async clearCache(pattern) {
        try {
            const allKeys = await chrome.storage.local.get();
            const keysToRemove = Object.keys(allKeys).filter(key => {
                if (pattern) {
                    return key.startsWith('cache_') && key.includes(pattern);
                }
                return key.startsWith('cache_');
            });

            if (keysToRemove.length > 0) {
                await chrome.storage.local.remove(keysToRemove);
                console.log(`[Background] 已清除 ${keysToRemove.length} 个缓存项`);
            }
        } catch (error) {
            console.error('[Background] 清除缓存失败:', error);
        }
    }

    // 更新检核状态
    async updateInspectionStatus(data) {
        try {
            const settings = await this.getSettings();
            const updateUrl = settings.apiEndpoints?.update;
            
            if (!updateUrl) {
                throw new Error('未配置更新接口地址');
            }

            // 构造请求数据
            const requestData = {
                markId: data.markId,
                markFlag: data.markFlag,
                markReason: data.markReason || ''
            };

            console.log('[Background] 发送更新请求:', requestData);

            // 发送请求到当前活动的标签页
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tabs.length === 0) {
                throw new Error('未找到活动标签页');
            }

            const response = await chrome.tabs.sendMessage(tabs[0].id, {
                type: 'MAKE_API_REQUEST',
                url: updateUrl,
                method: 'POST',
                data: requestData
            });

            if (response.success) {
                // 更新缓存中的数据
                await this.updateCacheInspectionStatus(data);
                console.log('[Background] 检核状态更新成功');
                return { success: true, data: response.data };
            } else {
                throw new Error(response.error || '更新失败');
            }
        } catch (error) {
            console.error('[Background] 更新检核状态失败:', error);
            return { success: false, error: error.message };
        }
    }

    // 更新缓存中的检核状态
    async updateCacheInspectionStatus(data) {
        try {
            // 获取所有相关缓存
            const allKeys = await chrome.storage.local.get();
            const cacheKeys = Object.keys(allKeys).filter(key => key.startsWith('cache_'));

            for (const key of cacheKeys) {
                const cacheData = allKeys[key];
                if (cacheData && cacheData.data && cacheData.data.rows) {
                    let updated = false;
                    
                    // 遍历活动数据
                    for (const row of cacheData.data.rows) {
                        if (row.items) {
                            for (const item of row.items) {
                                if (item.rmarkLst) {
                                    for (const mark of item.rmarkLst) {
                                        if (mark.markId === data.markId) {
                                            mark.markFlag = data.markFlag;
                                            mark.markReason = data.markReason || '';
                                            updated = true;
                                            console.log(`[Background] 更新缓存中的检核状态: ${key}`);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if (updated) {
                        await chrome.storage.local.set({ [key]: cacheData });
                    }
                }
            }
        } catch (error) {
            console.error('[Background] 更新缓存检核状态失败:', error);
        }
    }

    // 执行AI检核
    async performAIInspection(data) {
        try {
            const settings = await this.getSettings();
            const aiConfig = settings.aiConfig;

            if (!aiConfig.enabled || !aiConfig.apiUrl || !aiConfig.apiKey) {
                throw new Error('AI检核未启用或配置不完整');
            }

            console.log('[Background] 开始AI检核:', data.imageUrl);

            const response = await fetch(aiConfig.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${aiConfig.apiKey}`
                },
                body: JSON.stringify({
                    image_url: data.imageUrl,
                    activity_type: data.activityType,
                    check_item: data.checkItem
                })
            });

            if (!response.ok) {
                throw new Error(`AI接口请求失败: ${response.status}`);
            }

            const result = await response.json();
            
            // 处理AI检核结果
            const aiResult = {
                success: true,
                confidence: result.confidence || 0,
                result: result.result || 'unknown',
                reason: result.reason || '',
                isLowConfidence: (result.confidence || 0) < aiConfig.confidenceThreshold
            };

            console.log('[Background] AI检核完成:', aiResult);
            return aiResult;
        } catch (error) {
            console.error('[Background] AI检核失败:', error);
            return { success: false, error: error.message };
        }
    }

    // 设置网络拦截器
    setupNetworkInterceptor() {
        // 这里可以添加网络请求拦截逻辑
        console.log('[Background] 网络拦截器已设置');
    }

    // 日志记录
    log(level, message, data) {
        const timestamp = new Date().toISOString();
        const logEntry = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
        
        if (data) {
            console.log(logEntry, data);
        } else {
            console.log(logEntry);
        }

        // 可以选择将日志保存到storage中
        this.saveLog(level, message, data, timestamp);
    }

    // 保存日志到storage
    async saveLog(level, message, data, timestamp) {
        try {
            const logKey = `log_${new Date().toISOString().split('T')[0]}`;
            const existing = await chrome.storage.local.get(logKey);
            const logs = existing[logKey] || [];
            
            logs.push({
                timestamp,
                level,
                message,
                data: data ? JSON.stringify(data) : null
            });

            // 限制日志数量，避免占用过多存储空间
            if (logs.length > 1000) {
                logs.splice(0, logs.length - 1000);
            }

            await chrome.storage.local.set({ [logKey]: logs });
        } catch (error) {
            console.error('[Background] 保存日志失败:', error);
        }
    }
}

// 初始化后台脚本
new ExtensionBackground();