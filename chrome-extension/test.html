<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片检核管理扩展 - 测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 16px;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .test-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .test-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .test-card h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 16px;
        }
        
        .test-card p {
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success {
            background: #28a745;
        }
        
        .status-warning {
            background: #ffc107;
        }
        
        .status-error {
            background: #dc3545;
        }
        
        .log-container {
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .mock-data {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .mock-data pre {
            margin: 0;
            font-size: 12px;
            color: #495057;
            overflow-x: auto;
        }
        
        .extension-status {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 4px solid #2196f3;
        }
        
        .extension-status h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .status-item:last-child {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖼️ 图片检核管理扩展</h1>
            <p>测试页面 - 验证扩展功能</p>
        </div>
        
        <div class="content">
            <!-- 扩展状态 -->
            <div class="extension-status">
                <h3>📊 扩展状态检测</h3>
                <div class="status-item">
                    <span class="status-indicator status-warning" id="extension-indicator"></span>
                    <span id="extension-status">检测中...</span>
                </div>
                <div class="status-item">
                    <span class="status-indicator status-warning" id="injection-indicator"></span>
                    <span id="injection-status">脚本注入状态检测中...</span>
                </div>
                <div class="status-item">
                    <span class="status-indicator status-warning" id="api-indicator"></span>
                    <span id="api-status">API拦截状态检测中...</span>
                </div>
            </div>
            
            <!-- 基础功能测试 -->
            <div class="section">
                <h2>🧪 基础功能测试</h2>
                <div class="test-grid">
                    <div class="test-card">
                        <h3>📡 网络请求模拟</h3>
                        <p>模拟查询接口请求，测试数据拦截功能</p>
                        <button class="btn" onclick="simulateQuery()">模拟查询请求</button>
                        <button class="btn btn-secondary" onclick="simulateUpdate()">模拟更新请求</button>
                    </div>
                    
                    <div class="test-card">
                        <h3>💾 缓存功能测试</h3>
                        <p>测试数据缓存、读取和清除功能</p>
                        <button class="btn" onclick="testCache()">测试缓存</button>
                        <button class="btn btn-warning" onclick="clearTestCache()">清除缓存</button>
                    </div>
                    
                    <div class="test-card">
                        <h3>🖼️ 图片检核界面</h3>
                        <p>打开图片检核管理界面</p>
                        <button class="btn btn-success" onclick="openInspectionModal()">打开检核界面</button>
                        <button class="btn btn-secondary" onclick="loadMockData()">加载模拟数据</button>
                    </div>
                    
                    <div class="test-card">
                        <h3>🤖 AI功能测试</h3>
                        <p>测试AI图片检核功能</p>
                        <button class="btn" onclick="testAI()">测试AI检核</button>
                        <button class="btn btn-secondary" onclick="batchAITest()">批量AI测试</button>
                    </div>
                </div>
            </div>
            
            <!-- 模拟数据 -->
            <div class="section">
                <h2>📋 模拟数据</h2>
                <p>以下是用于测试的模拟API响应数据：</p>
                <div class="mock-data">
                    <pre id="mock-data-display">加载中...</pre>
                </div>
            </div>
            
            <!-- 操作日志 -->
            <div class="section">
                <h2>📝 操作日志</h2>
                <button class="btn btn-secondary" onclick="clearLog()">清除日志</button>
                <button class="btn btn-secondary" onclick="exportLog()">导出日志</button>
                <div class="log-container" id="log-container">
                    <div>[INFO] 测试页面已加载</div>
                    <div>[INFO] 等待扩展检测...</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 测试页面JavaScript
        let logContainer = document.getElementById('log-container');
        let mockData = null;
        
        // 日志函数
        function log(level, message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${level}] ${timestamp} - ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[TestPage] ${level}: ${message}`);
        }
        
        // 检测扩展状态
        function checkExtensionStatus() {
            log('INFO', '开始检测扩展状态...');
            
            // 检测扩展是否存在
            if (window.inspectionManager) {
                updateStatus('extension', 'success', '扩展已加载');
                updateStatus('injection', 'success', '脚本已注入');
                log('SUCCESS', '检测到图片检核管理扩展');
            } else {
                updateStatus('extension', 'error', '扩展未检测到');
                updateStatus('injection', 'error', '脚本未注入');
                log('ERROR', '未检测到图片检核管理扩展');
            }
            
            // 检测API拦截
            if (window.fetch !== window.originalFetch) {
                updateStatus('api', 'success', 'API拦截已启用');
                log('SUCCESS', 'API拦截功能已启用');
            } else {
                updateStatus('api', 'warning', 'API拦截未启用');
                log('WARNING', 'API拦截功能未启用');
            }
        }
        
        function updateStatus(type, status, text) {
            const indicator = document.getElementById(`${type}-indicator`);
            const statusText = document.getElementById(`${type}-status`);
            
            indicator.className = `status-indicator status-${status}`;
            statusText.textContent = text;
        }
        
        // 模拟查询请求
        function simulateQuery() {
            log('INFO', '模拟查询请求...');
            
            const mockResponse = {
                result: "1",
                rows: [
                    {
                        actId: "ACT001",
                        actName: "测试活动A",
                        items: [
                            {
                                checkItemTitle: "外观检查",
                                rmarkLst: [
                                    {
                                        markId: "MARK001",
                                        markFlag: "1",
                                        markReason: "",
                                        checkItemValue: "https://picsum.photos/400/300?random=1"
                                    },
                                    {
                                        markId: "MARK002", 
                                        markFlag: "2",
                                        markReason: "质量问题",
                                        checkItemValue: "https://picsum.photos/400/300?random=2"
                                    }
                                ]
                            }
                        ]
                    }
                ]
            };
            
            // 模拟fetch请求
            fetch('/ly/mp/busicen/acc/accCheckInfo/queryPage.do', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ test: true })
            }).then(response => {
                log('SUCCESS', '查询请求已发送');
                return response.json();
            }).catch(error => {
                log('ERROR', '查询请求失败: ' + error.message);
            });
            
            // 直接触发数据处理
            if (window.inspectionManager) {
                window.inspectionManager.handleQueryResponse({
                    json: () => Promise.resolve(mockResponse)
                });
                log('SUCCESS', '模拟数据已处理');
            }
        }
        
        // 模拟更新请求
        function simulateUpdate() {
            log('INFO', '模拟更新请求...');
            
            fetch('/ly/mp/busicen/acc/accCheckRecordInfo/updateById.do', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    markId: 'MARK001',
                    markFlag: '1',
                    markReason: '测试更新'
                })
            }).then(response => {
                log('SUCCESS', '更新请求已发送');
            }).catch(error => {
                log('ERROR', '更新请求失败: ' + error.message);
            });
        }
        
        // 测试缓存功能
        function testCache() {
            log('INFO', '测试缓存功能...');
            
            const testData = {
                timestamp: Date.now(),
                data: { test: 'cache data' }
            };
            
            localStorage.setItem('test_cache', JSON.stringify(testData));
            
            const retrieved = localStorage.getItem('test_cache');
            if (retrieved) {
                log('SUCCESS', '缓存测试成功');
            } else {
                log('ERROR', '缓存测试失败');
            }
        }
        
        // 清除测试缓存
        function clearTestCache() {
            localStorage.removeItem('test_cache');
            log('INFO', '测试缓存已清除');
        }
        
        // 打开检核界面
        function openInspectionModal() {
            log('INFO', '尝试打开检核界面...');
            
            if (window.inspectionManager) {
                window.inspectionManager.showInspectionModal();
                log('SUCCESS', '检核界面已打开');
            } else {
                log('ERROR', '扩展未加载，无法打开检核界面');
            }
        }
        
        // 加载模拟数据
        function loadMockData() {
            log('INFO', '加载模拟数据...');
            simulateQuery();
        }
        
        // 测试AI功能
        function testAI() {
            log('INFO', '测试AI功能...');
            log('WARNING', 'AI功能需要配置API密钥');
        }
        
        // 批量AI测试
        function batchAITest() {
            log('INFO', '批量AI测试...');
            log('WARNING', '批量AI测试需要配置API密钥');
        }
        
        // 清除日志
        function clearLog() {
            logContainer.innerHTML = '';
            log('INFO', '日志已清除');
        }
        
        // 导出日志
        function exportLog() {
            const logs = logContainer.textContent;
            const blob = new Blob([logs], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `inspection-test-log-${Date.now()}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            log('INFO', '日志已导出');
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('INFO', '测试页面初始化完成');
            
            // 延迟检测扩展状态
            setTimeout(checkExtensionStatus, 1000);
            
            // 定期检测
            setInterval(checkExtensionStatus, 5000);
            
            // 显示模拟数据
            const mockDataDisplay = document.getElementById('mock-data-display');
            mockDataDisplay.textContent = JSON.stringify({
                result: "1",
                rows: [
                    {
                        actId: "ACT001",
                        actName: "测试活动",
                        items: [
                            {
                                checkItemTitle: "外观检查",
                                rmarkLst: [
                                    {
                                        markId: "MARK001",
                                        markFlag: "1",
                                        checkItemValue: "https://picsum.photos/400/300"
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }, null, 2);
        });
    </script>
</body>
</html>
