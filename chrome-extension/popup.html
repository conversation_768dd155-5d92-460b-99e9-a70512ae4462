<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>检核图片管理扩展</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            width: 350px;
            min-height: 400px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow-x: hidden;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 18px;
            color: #333;
            margin-bottom: 5px;
        }
        
        .header p {
            font-size: 12px;
            color: #666;
        }
        
        .status-section {
            background: rgba(255, 255, 255, 0.9);
            margin: 10px;
            padding: 15px;
            border-radius: 8px;
            backdrop-filter: blur(5px);
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .status-item:last-child {
            margin-bottom: 0;
            border-bottom: none;
        }
        
        .status-label {
            font-size: 13px;
            color: #555;
        }
        
        .status-value {
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;
        }
        
        .status-active {
            background: #28a745;
        }
        
        .status-inactive {
            background: #dc3545;
        }
        
        .status-warning {
            background: #ffc107;
        }
        
        .actions-section {
            margin: 10px;
        }
        
        .action-btn {
            width: 100%;
            padding: 12px;
            margin-bottom: 8px;
            border: none;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .action-btn:last-child {
            margin-bottom: 0;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
            transform: translateY(-1px);
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
            transform: translateY(-1px);
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
            transform: translateY(-1px);
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-1px);
        }
        
        .btn-disabled {
            background: #e9ecef;
            color: #6c757d;
            cursor: not-allowed;
        }
        
        .btn-disabled:hover {
            transform: none;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.8);
            padding: 12px;
            border-radius: 6px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
            display: block;
        }
        
        .stat-label {
            font-size: 11px;
            color: #666;
            margin-top: 2px;
        }
        
        .current-page {
            background: rgba(255, 255, 255, 0.9);
            margin: 10px;
            padding: 12px;
            border-radius: 8px;
            font-size: 12px;
        }
        
        .current-page-title {
            font-weight: 500;
            margin-bottom: 5px;
            color: #333;
        }
        
        .current-page-url {
            color: #666;
            word-break: break-all;
            line-height: 1.4;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 10px;
        }
        
        .quick-btn {
            padding: 8px;
            border: none;
            border-radius: 4px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .footer {
            background: rgba(255, 255, 255, 0.8);
            padding: 10px;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
        
        .loading {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .hidden {
            display: none;
        }
        
        .alert {
            margin: 10px;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            border: 1px solid transparent;
        }
        
        .alert-success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .alert-error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .alert-info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 检核图片管理</h1>
        <p>Chrome扩展 v1.0.0</p>
    </div>
    
    <div id="alert-container"></div>
    
    <div class="current-page">
        <div class="current-page-title">当前页面</div>
        <div class="current-page-url" id="current-url">加载中...</div>
    </div>
    
    <div class="status-section">
        <div class="status-item">
            <span class="status-label">扩展状态</span>
            <span class="status-value">
                <span class="status-indicator" id="extension-status"></span>
                <span id="extension-status-text">检查中...</span>
            </span>
        </div>
        
        <div class="status-item">
            <span class="status-label">页面匹配</span>
            <span class="status-value">
                <span class="status-indicator" id="page-match-status"></span>
                <span id="page-match-text">检查中...</span>
            </span>
        </div>
        
        <div class="status-item">
            <span class="status-label">脚本注入</span>
            <span class="status-value">
                <span class="status-indicator" id="injection-status"></span>
                <span id="injection-text">检查中...</span>
            </span>
        </div>
        
        <div class="status-item">
            <span class="status-label">AI功能</span>
            <span class="status-value">
                <span class="status-indicator" id="ai-status"></span>
                <span id="ai-text">检查中...</span>
            </span>
        </div>
    </div>
    
    <div class="status-section">
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-number" id="cache-count">-</span>
                <div class="stat-label">缓存项目</div>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="cache-size">-</span>
                <div class="stat-label">缓存大小</div>
            </div>
        </div>
        
        <div class="quick-actions">
            <button class="quick-btn btn-secondary" onclick="refreshCache()">刷新缓存</button>
            <button class="quick-btn btn-warning" onclick="clearCache()">清除缓存</button>
        </div>
    </div>
    
    <div class="actions-section">
        <button class="action-btn btn-primary" id="inject-btn" onclick="injectScript()">
            <span>🚀</span>
            <span>注入检核脚本</span>
        </button>
        
        <button class="action-btn btn-success" id="open-inspector" onclick="openInspector()">
            <span>🖼️</span>
            <span>打开图片检核</span>
        </button>
        
        <button class="action-btn btn-secondary" onclick="openOptions()">
            <span>⚙️</span>
            <span>扩展设置</span>
        </button>
        
        <button class="action-btn btn-warning" id="debug-btn" onclick="toggleDebug()">
            <span>🐛</span>
            <span id="debug-text">启用调试</span>
        </button>
    </div>
    
    <div class="footer">
        <div>最后更新: <span id="last-update">-</span></div>
        <div style="margin-top: 5px;">
            <a href="#" onclick="openHelp()" style="color: #007bff; text-decoration: none;">使用帮助</a>
            |
            <a href="#" onclick="reportIssue()" style="color: #007bff; text-decoration: none;">反馈问题</a>
        </div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>