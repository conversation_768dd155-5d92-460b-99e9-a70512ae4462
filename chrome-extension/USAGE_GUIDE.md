# 图片检核管理扩展 - 使用指南

## 🎉 当前状态

✅ **扩展已成功加载**
- 修复版脚本正常工作
- 无 "Illegal invocation" 错误
- 成功提取 actId: `9dde60eb18bdb03293bc1d645d3854c0`
- 按钮已创建并可用

## 🔧 可用的调试命令

在浏览器Console中运行以下命令：

### 基础检查
```javascript
// 检查扩展状态
debugFixed.checkStatus()

// 查看当前配置
debugFixed.showConfig()
```

### 数据操作
```javascript
// 模拟API数据（用于测试）
debugFixed.simulateData()

// 打开检核界面
debugFixed.openModal()

// 清除缓存
debugFixed.clearCache()
```

### 网络监控
```javascript
// 开始监控所有网络请求
debugFixed.monitorRequests()

// 停止监控
debugFixed.stopMonitoring()
```

## 📊 当前页面信息

- **页面URL**: https://assttest.dna-nev.com.cn/#/veModule/marketFee/marketBasicData/recordViewing
- **ActId**: 9dde60eb18bdb03293bc1d645d3854c0
- **缓存键**: inspection_9dde60eb18bdb03293bc1d645d3854c0_2025-07-17

## 🎯 下一步操作

### 1. 找到实际的查询接口

当前扩展在监听：`/ly/mp/busicen/acc/accCheckInfo/queryPage.do`

但页面实际使用的可能是不同的接口。运行以下命令来监控：

```javascript
// 开始监控网络请求
debugFixed.monitorRequests()

// 然后在页面上点击查询按钮，观察Console输出
```

### 2. 测试模拟数据功能

```javascript
// 生成模拟数据
debugFixed.simulateData()

// 打开检核界面查看
debugFixed.openModal()
```

### 3. 检查页面上的查询按钮

页面上应该有一个"🖼️ 检核管理"按钮。如果没有看到，可能是因为：
- 按钮选择器配置需要调整
- 页面结构与预期不同

## 🔍 故障排除

### 问题1: 没有拦截到查询请求
**可能原因**: API路径不匹配
**解决方案**:
1. 运行 `debugFixed.monitorRequests()`
2. 点击页面上的查询按钮
3. 查看Console中显示的实际请求URL
4. 如果需要，更新API路径配置

### 问题2: 检核界面显示"无缓存数据"
**原因**: 还没有拦截到有效的API响应
**解决方案**:
1. 使用 `debugFixed.simulateData()` 生成测试数据
2. 或者确保API拦截正常工作

### 问题3: 按钮未出现
**可能原因**: 按钮选择器问题已修复
**验证**: 页面上应该能看到"🖼️ 检核管理"按钮

## 📝 实际使用流程

### 标准流程
1. **访问目标页面** ✅ 已完成
2. **扩展自动加载** ✅ 已完成
3. **点击查询按钮** - 在页面上找到并点击查询按钮
4. **扩展拦截数据** - 扩展会自动拦截API响应
5. **打开检核界面** - 点击"🖼️ 检核管理"按钮

### 测试流程
1. **生成模拟数据**: `debugFixed.simulateData()`
2. **打开检核界面**: `debugFixed.openModal()`
3. **查看数据结构**: 界面会显示JSON格式的数据

## 🎨 界面功能

当前的检核界面包含：
- **标题栏**: 显示当前actId
- **数据显示**: JSON格式显示所有检核数据
- **关闭按钮**: 点击关闭界面
- **遮罩关闭**: 点击背景区域关闭

## 🚀 高级功能

### 缓存管理
- 数据按actId和日期自动分组
- 30分钟自动过期
- 支持手动清除

### 网络拦截
- 支持fetch和XMLHttpRequest
- 自动解析JSON响应
- 错误处理和重试机制

### 调试工具
- 实时状态监控
- 网络请求监控
- 模拟数据生成

## 📞 获取帮助

如果遇到问题：

1. **检查Console**: 查看是否有错误信息
2. **运行诊断**: `debugFixed.checkStatus()`
3. **监控网络**: `debugFixed.monitorRequests()`
4. **查看配置**: `debugFixed.showConfig()`

## 🎯 成功指标

✅ 扩展已加载  
✅ 无JavaScript错误  
✅ 按钮已创建  
✅ 调试工具可用  
✅ 模拟数据功能正常  
✅ 检核界面可打开  

**下一步**: 找到实际的查询接口并配置正确的API路径。

---

**提示**: 使用 `debugFixed.monitorRequests()` 然后点击页面上的查询按钮，可以帮助我们找到正确的API接口路径。
