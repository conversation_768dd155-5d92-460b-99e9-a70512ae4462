// 图片检核管理扩展 - 注入脚本
(() => {
    'use strict';
    
    console.log('[Injected] 检核管理脚本开始执行');
    
    class InspectionManager {
        constructor() {
            this.config = null;
            this.currentData = null;
            this.isInitialized = false;
            this.requestId = 0;
            this.pendingRequests = new Map();
            
            this.init();
        }
        
        init() {
            console.log('[Injected] 初始化检核管理器');
            this.setupMessageListener();
            this.waitForConfig();
        }
        
        setupMessageListener() {
            window.addEventListener('message', (event) => {
                if (event.source !== window) return;
                this.handleMessage(event.data);
            });
        }
        
        waitForConfig() {
            const checkConfig = () => {
                if (this.config) {
                    this.onConfigReady();
                } else {
                    setTimeout(checkConfig, 100);
                }
            };
            checkConfig();
        }
        
        onConfigReady() {
            console.log('[Injected] 配置已就绪，开始初始化功能');
            this.setupNetworkInterception();
            this.addMainButton();
            this.isInitialized = true;
        }
        
        handleMessage(data) {
            switch (data.type) {
                case 'EXTENSION_CONFIG':
                    this.config = data.data;
                    console.log('[Injected] 收到配置:', this.config);
                    break;
                    
                case 'CONTENT_CACHE_RESPONSE':
                    this.handleCacheResponse(data);
                    break;
                    
                case 'CONTENT_UPDATE_STATUS_RESPONSE':
                    this.handleUpdateStatusResponse(data);
                    break;
                    
                case 'CONTENT_AI_INSPECTION_RESPONSE':
                    this.handleAIInspectionResponse(data);
                    break;
            }
        }
        
        setupNetworkInterception() {
            if (!this.config || !this.config.apiEndpoints) {
                console.warn('[Injected] 无API配置，跳过网络拦截设置');
                return;
            }
            
            const queryEndpoint = this.config.apiEndpoints.query;
            
            // 拦截fetch请求
            const originalFetch = window.fetch;
            window.fetch = async (...args) => {
                const response = await originalFetch.apply(this, args);
                
                if (args[0] && args[0].includes && args[0].includes(queryEndpoint)) {
                    this.handleQueryResponse(response.clone());
                }
                
                return response;
            };
            
            // 拦截XMLHttpRequest
            const originalXHROpen = XMLHttpRequest.prototype.open;
            const originalXHRSend = XMLHttpRequest.prototype.send;
            
            XMLHttpRequest.prototype.open = function(method, url, ...rest) {
                this._url = url;
                this._method = method;
                return originalXHROpen.apply(this, [method, url, ...rest]);
            };
            
            XMLHttpRequest.prototype.send = function(data) {
                if (this._url && this._url.includes(queryEndpoint)) {
                    this.addEventListener('load', () => {
                        if (this.readyState === 4 && this.status === 200) {
                            try {
                                const responseData = JSON.parse(this.responseText);
                                window.inspectionManager.handleQueryResponse({
                                    json: () => Promise.resolve(responseData)
                                });
                            } catch (error) {
                                console.error('[Injected] XHR响应解析失败:', error);
                            }
                        }
                    });
                }
                return originalXHRSend.apply(this, [data]);
            };
            
            console.log('[Injected] 网络拦截已设置');
        }
        
        async handleQueryResponse(response) {
            try {
                const data = await response.json();
                console.log('[Injected] 拦截到查询响应:', data);
                
                if (data.result === '1' && data.rows) {
                    // 缓存数据
                    const cacheKey = this.generateCacheKey();
                    await this.setCache(cacheKey, data);
                    this.currentData = data;
                    console.log('[Injected] 查询数据已缓存');
                }
            } catch (error) {
                console.error('[Injected] 处理查询响应失败:', error);
            }
        }
        
        addMainButton() {
            // 检查按钮是否已存在
            if (document.getElementById('inspection-main-btn')) {
                return;
            }
            
            const button = document.createElement('button');
            button.id = 'inspection-main-btn';
            button.innerHTML = '🖼️ 检核管理';
            button.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                z-index: 9999;
                padding: 12px 18px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 600;
                box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                backdrop-filter: blur(10px);
                user-select: none;
            `;
            
            // 添加悬停效果
            button.addEventListener('mouseenter', () => {
                button.style.transform = 'translateY(-3px) scale(1.05)';
                button.style.boxShadow = '0 8px 30px rgba(102, 126, 234, 0.6)';
            });
            
            button.addEventListener('mouseleave', () => {
                button.style.transform = 'translateY(0) scale(1)';
                button.style.boxShadow = '0 4px 20px rgba(102, 126, 234, 0.4)';
            });
            
            button.addEventListener('click', () => {
                this.showInspectionModal();
            });
            
            document.body.appendChild(button);
            console.log('[Injected] 主操作按钮已添加');
        }
        
        async showInspectionModal() {
            console.log('[Injected] 显示检核弹窗');
            
            try {
                // 获取缓存数据
                const cacheKey = this.generateCacheKey();
                const cachedData = await this.getCache(cacheKey);
                
                if (cachedData) {
                    this.createInspectionModal(cachedData);
                } else {
                    // 尝试触发查询
                    const triggered = this.triggerQuery();
                    if (triggered) {
                        this.showLoadingModal();
                        // 等待数据
                        setTimeout(async () => {
                            const newData = await this.getCache(cacheKey);
                            this.hideLoadingModal();
                            if (newData) {
                                this.createInspectionModal(newData);
                            } else {
                                this.showErrorModal('未能获取到数据，请手动点击查询按钮后重试');
                            }
                        }, 5000);
                    } else {
                        this.showErrorModal('请先手动点击页面上的查询按钮，然后重试');
                    }
                }
            } catch (error) {
                console.error('[Injected] 显示检核弹窗失败:', error);
                this.showErrorModal('显示弹窗失败: ' + error.message);
            }
        }
        
        triggerQuery() {
            if (!this.config || !this.config.buttonSelectors) {
                return false;
            }
            
            for (const selector of this.config.buttonSelectors) {
                const button = document.querySelector(selector);
                if (button && button.offsetParent !== null) {
                    console.log('[Injected] 触发查询按钮:', selector);
                    button.click();
                    return true;
                }
            }
            
            console.warn('[Injected] 未找到可用的查询按钮');
            return false;
        }
        
        generateCacheKey() {
            const url = window.location.href.split('?')[0].split('#')[0];
            const date = new Date().toISOString().split('T')[0];
            return `${url}_${date}`;
        }
        
        // 缓存操作
        async getCache(key) {
            return new Promise((resolve) => {
                const requestId = ++this.requestId;
                this.pendingRequests.set(requestId, resolve);
                
                window.postMessage({
                    type: 'INJECTED_GET_CACHE',
                    requestId: requestId,
                    key: key
                }, '*');
                
                setTimeout(() => {
                    if (this.pendingRequests.has(requestId)) {
                        this.pendingRequests.delete(requestId);
                        resolve(null);
                    }
                }, 5000);
            });
        }
        
        async setCache(key, data, expireTime) {
            return new Promise((resolve) => {
                const requestId = ++this.requestId;
                this.pendingRequests.set(requestId, resolve);
                
                window.postMessage({
                    type: 'INJECTED_SET_CACHE',
                    requestId: requestId,
                    key: key,
                    data: data,
                    expireTime: expireTime
                }, '*');
                
                setTimeout(() => {
                    if (this.pendingRequests.has(requestId)) {
                        this.pendingRequests.delete(requestId);
                        resolve(false);
                    }
                }, 5000);
            });
        }
        
        handleCacheResponse(data) {
            const resolve = this.pendingRequests.get(data.requestId);
            if (resolve) {
                this.pendingRequests.delete(data.requestId);
                resolve(data.data);
            }
        }
        
        handleUpdateStatusResponse(data) {
            const resolve = this.pendingRequests.get(data.requestId);
            if (resolve) {
                this.pendingRequests.delete(data.requestId);
                resolve(data.result);
            }
        }
        
        handleAIInspectionResponse(data) {
            const resolve = this.pendingRequests.get(data.requestId);
            if (resolve) {
                this.pendingRequests.delete(data.requestId);
                resolve(data.result);
            }
        }

        // 创建检核弹窗
        createInspectionModal(data) {
            this.hideAllModals();

            const items = this.parseData(data);
            if (!items.length) {
                this.showErrorModal('没有找到图片数据');
                return;
            }

            const modal = document.createElement('div');
            modal.id = 'inspection-main-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(0, 0, 0, 0.9);
                z-index: 999999;
                display: flex;
                flex-direction: column;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;

            // 创建工具栏
            const toolbar = this.createToolbar(items);
            modal.appendChild(toolbar);

            // 创建图片容器
            const container = this.createImageContainer(items);
            modal.appendChild(container);

            document.body.appendChild(modal);

            // 显示动画
            setTimeout(() => {
                modal.style.opacity = '1';
            }, 10);

            // 绑定事件
            this.bindModalEvents(modal, items);

            // 初始化懒加载
            this.initLazyLoading(container);
        }

        parseData(rawData) {
            if (!rawData || !rawData.rows) {
                console.log('[Parse] 无效的数据结构');
                return [];
            }

            const items = [];

            rawData.rows.forEach(row => {
                if (row.items) {
                    row.items.forEach(item => {
                        if (item.rmarkLst) {
                            item.rmarkLst.forEach(mark => {
                                if (mark.checkItemValue) {
                                    items.push({
                                        id: mark.markId || Date.now() + Math.random(),
                                        actId: row.actId,
                                        actName: row.actName || '未知活动',
                                        itemTitle: item.checkItemTitle || '未知项目',
                                        flag: mark.markFlag === '1' ? '合格' : mark.markFlag === '2' ? '不合格' : '未点检',
                                        flagValue: mark.markFlag,
                                        color: mark.markFlag === '1' ? '#67c23a' : mark.markFlag === '2' ? '#f56c6c' : '#909399',
                                        imageUrl: mark.checkItemValue,
                                        reason: mark.markReason || '',
                                        rawData: mark,
                                        aiResult: null,
                                        aiConfidence: null
                                    });
                                }
                            });
                        }
                    });
                }
            });

            console.log(`[Parse] 解析到 ${items.length} 个检核项目`);
            return items;
        }

        createToolbar(items) {
            const toolbar = document.createElement('div');
            toolbar.style.cssText = `
                background: #fff;
                padding: 15px 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 1px solid #ddd;
                flex-shrink: 0;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            `;

            toolbar.innerHTML = `
                <div style="display: flex; gap: 15px; align-items: center;">
                    <h3 style="margin: 0; color: #333; font-size: 18px;">🖼️ 图片检核管理 (${items.length}张)</h3>
                    <button id="refresh-btn" style="padding: 8px 16px; background: #409eff; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s;">
                        🔄 刷新数据
                    </button>
                    <button id="clear-cache-btn" style="padding: 8px 16px; background: #f56c6c; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s;">
                        🗑️ 清除缓存
                    </button>
                </div>
                <div style="display: flex; gap: 15px; align-items: center;">
                    <select id="filter-select" style="padding: 8px 12px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px;">
                        <option value="all">全部状态</option>
                        <option value="1">✅ 合格</option>
                        <option value="2">❌ 不合格</option>
                        <option value="0">⏳ 未点检</option>
                        <option value="ai-low">🤖 AI低可信度</option>
                    </select>
                    <button id="ai-check-btn" style="padding: 8px 16px; background: #67c23a; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s;">
                        🤖 批量AI检核
                    </button>
                    <button id="fullscreen-btn" style="padding: 8px 16px; background: #909399; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s;">
                        📺 全屏
                    </button>
                    <button id="close-btn" style="padding: 8px 16px; background: #f56c6c; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s;">
                        ✖️ 关闭
                    </button>
                </div>
            `;

            return toolbar;
        }

        createImageContainer(items) {
            const container = document.createElement('div');
            container.id = 'image-container';
            container.style.cssText = `
                flex: 1;
                overflow-y: auto;
                padding: 20px;
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
                gap: 20px;
                background: #f8f9fa;
            `;

            items.forEach((item, index) => {
                const card = this.createImageCard(item, index);
                container.appendChild(card);
            });

            return container;
        }

        createImageCard(item, index) {
            const card = document.createElement('div');
            card.className = 'image-card';
            card.dataset.flag = item.flagValue;
            card.dataset.index = index;
            card.style.cssText = `
                background: white;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                border: 2px solid transparent;
            `;

            card.innerHTML = `
                <div style="position: relative; overflow: hidden;">
                    <img data-src="${item.imageUrl}" alt="检核图片"
                         style="width: 100%; height: 220px; object-fit: cover; cursor: pointer; background: #f0f0f0; transition: transform 0.3s;"
                         class="lazy-image" />
                    <div style="position: absolute; top: 12px; right: 12px; background: ${item.color}; color: white; padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; box-shadow: 0 2px 8px rgba(0,0,0,0.3);">
                        ${item.flag}
                    </div>
                    ${item.aiResult ? `
                        <div style="position: absolute; top: 12px; left: 12px; background: ${item.aiConfidence < 60 ? '#ff6b6b' : '#4ecdc4'}; color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: 600;">
                            AI: ${item.aiConfidence}%
                        </div>
                    ` : ''}
                </div>
                <div style="padding: 16px;">
                    <h4 style="margin: 0 0 8px 0; color: #333; font-size: 15px; font-weight: 600; line-height: 1.3;">${item.actName}</h4>
                    <p style="margin: 0 0 12px 0; color: #666; font-size: 13px; line-height: 1.4;">${item.itemTitle}</p>
                    <div style="display: flex; justify-content: space-between; align-items: center; gap: 10px;">
                        <span style="color: ${item.color}; font-weight: 600; font-size: 13px; display: flex; align-items: center;">
                            ${item.flag}
                        </span>
                        <div style="display: flex; gap: 8px;">
                            <button class="ai-check-btn" data-index="${index}"
                                    style="padding: 6px 12px; background: #4ecdc4; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 12px; transition: all 0.3s;">
                                🤖 AI检核
                            </button>
                            <button class="update-btn" data-index="${index}" data-flag="${item.flagValue}"
                                    style="padding: 6px 12px; background: #409eff; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 12px; transition: all 0.3s;">
                                ✏️ 更新
                            </button>
                        </div>
                    </div>
                </div>
            `;

            return card;
        }

        bindModalEvents(modal, items) {
            // 关闭按钮
            modal.querySelector('#close-btn').addEventListener('click', () => {
                modal.style.opacity = '0';
                setTimeout(() => modal.remove(), 300);
            });

            // 点击遮罩关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.style.opacity = '0';
                    setTimeout(() => modal.remove(), 300);
                }
            });

            // 刷新按钮
            modal.querySelector('#refresh-btn').addEventListener('click', async () => {
                this.showLoadingModal();
                const triggered = this.triggerQuery();
                if (triggered) {
                    setTimeout(async () => {
                        const cacheKey = this.generateCacheKey();
                        const newData = await this.getCache(cacheKey);
                        this.hideLoadingModal();
                        if (newData) {
                            modal.remove();
                            this.createInspectionModal(newData);
                        } else {
                            this.hideLoadingModal();
                            this.showToast('刷新失败，请重试');
                        }
                    }, 3000);
                } else {
                    this.hideLoadingModal();
                    this.showToast('请手动点击查询按钮');
                }
            });

            // 清除缓存按钮
            modal.querySelector('#clear-cache-btn').addEventListener('click', async () => {
                const cacheKey = this.generateCacheKey();
                await this.clearCache(cacheKey);
                this.showToast('缓存已清除');
            });

            // 全屏按钮
            modal.querySelector('#fullscreen-btn').addEventListener('click', () => {
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                } else {
                    modal.requestFullscreen();
                }
            });

            // 筛选
            modal.querySelector('#filter-select').addEventListener('change', (e) => {
                this.filterImages(modal, e.target.value);
            });

            // 批量AI检核
            modal.querySelector('#ai-check-btn').addEventListener('click', () => {
                this.batchAICheck(items);
            });

            // 图片点击事件
            modal.querySelectorAll('.lazy-image').forEach((img, index) => {
                img.addEventListener('click', () => {
                    this.showImageModal(items[index]);
                });
            });

            // 单个AI检核按钮
            modal.querySelectorAll('.ai-check-btn').forEach(btn => {
                btn.addEventListener('click', async (e) => {
                    e.stopPropagation();
                    const index = parseInt(btn.dataset.index);
                    await this.performSingleAICheck(items[index], btn);
                });
            });

            // 更新结果按钮
            modal.querySelectorAll('.update-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const index = parseInt(btn.dataset.index);
                    this.showUpdateModal(items[index], async (newFlag, reason) => {
                        await this.updateItemStatus(items[index], newFlag, reason, modal);
                    });
                });
            });
        }

        initLazyLoading(container) {
            const images = container.querySelectorAll('.lazy-image');
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.onload = () => {
                            img.style.opacity = '1';
                        };
                        imageObserver.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px'
            });

            images.forEach(img => imageObserver.observe(img));
        }

        filterImages(modal, filterValue) {
            const cards = modal.querySelectorAll('.image-card');
            cards.forEach(card => {
                const flag = card.dataset.flag;
                const isAILow = card.querySelector('[style*="AI:"]') &&
                               parseInt(card.querySelector('[style*="AI:"]').textContent.match(/\d+/)[0]) < 60;

                let shouldShow = false;

                switch (filterValue) {
                    case 'all':
                        shouldShow = true;
                        break;
                    case 'ai-low':
                        shouldShow = isAILow;
                        break;
                    default:
                        shouldShow = flag === filterValue;
                }

                card.style.display = shouldShow ? 'block' : 'none';
            });
        }

        async performSingleAICheck(item, button) {
            const originalText = button.textContent;
            button.textContent = '🔄 检核中...';
            button.disabled = true;

            try {
                const result = await this.callAIInspection(item);
                if (result.success) {
                    item.aiResult = result.result;
                    item.aiConfidence = result.confidence;

                    // 更新UI
                    const card = button.closest('.image-card');
                    const imageContainer = card.querySelector('div[style*="position: relative"]');

                    // 添加AI结果标签
                    const aiLabel = document.createElement('div');
                    aiLabel.style.cssText = `
                        position: absolute;
                        top: 12px;
                        left: 12px;
                        background: ${result.confidence < 60 ? '#ff6b6b' : '#4ecdc4'};
                        color: white;
                        padding: 4px 8px;
                        border-radius: 12px;
                        font-size: 11px;
                        font-weight: 600;
                    `;
                    aiLabel.textContent = `AI: ${result.confidence}%`;
                    imageContainer.appendChild(aiLabel);

                    this.showToast(`AI检核完成，可信度: ${result.confidence}%`);
                } else {
                    this.showToast('AI检核失败: ' + result.error);
                }
            } catch (error) {
                console.error('[AI检核] 失败:', error);
                this.showToast('AI检核失败');
            } finally {
                button.textContent = originalText;
                button.disabled = false;
            }
        }

        async callAIInspection(item) {
            return new Promise((resolve) => {
                const requestId = ++this.requestId;
                this.pendingRequests.set(requestId, resolve);

                window.postMessage({
                    type: 'INJECTED_AI_INSPECTION',
                    requestId: requestId,
                    data: {
                        imageUrl: item.imageUrl,
                        activityType: item.actName,
                        checkItem: item.itemTitle
                    }
                }, '*');

                setTimeout(() => {
                    if (this.pendingRequests.has(requestId)) {
                        this.pendingRequests.delete(requestId);
                        resolve({ success: false, error: 'AI检核请求超时' });
                    }
                }, 30000);
            });
        }

        // 显示加载弹窗
        showLoadingModal() {
            this.hideAllModals();

            const modal = document.createElement('div');
            modal.id = 'inspection-loading-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(0, 0, 0, 0.8);
                z-index: 999999;
                display: flex;
                align-items: center;
                justify-content: center;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    padding: 40px;
                    border-radius: 12px;
                    text-align: center;
                    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                    max-width: 300px;
                ">
                    <div style="
                        width: 40px;
                        height: 40px;
                        border: 4px solid #f3f3f3;
                        border-top: 4px solid #667eea;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                        margin: 0 auto 20px;
                    "></div>
                    <div style="font-size: 16px; color: #333; font-weight: 500;">正在加载检核数据...</div>
                </div>
                <style>
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                </style>
            `;

            document.body.appendChild(modal);
        }

        hideLoadingModal() {
            const modal = document.getElementById('inspection-loading-modal');
            if (modal) {
                modal.remove();
            }
        }

        // 显示错误弹窗
        showErrorModal(message) {
            this.hideAllModals();

            const modal = document.createElement('div');
            modal.id = 'inspection-error-modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(0, 0, 0, 0.8);
                z-index: 999999;
                display: flex;
                align-items: center;
                justify-content: center;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    padding: 30px;
                    border-radius: 12px;
                    text-align: center;
                    max-width: 400px;
                    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                ">
                    <div style="color: #f56c6c; font-size: 48px; margin-bottom: 20px;">⚠️</div>
                    <div style="font-size: 16px; color: #333; margin-bottom: 20px; line-height: 1.5;">${message}</div>
                    <button onclick="this.closest('#inspection-error-modal').remove()" style="
                        background: #667eea;
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 14px;
                        font-weight: 500;
                        transition: all 0.3s;
                    ">确定</button>
                </div>
            `;

            document.body.appendChild(modal);
        }

        hideAllModals() {
            const modals = [
                'inspection-loading-modal',
                'inspection-error-modal',
                'inspection-main-modal',
                'inspection-image-modal',
                'inspection-update-modal'
            ];

            modals.forEach(id => {
                const modal = document.getElementById(id);
                if (modal) {
                    modal.remove();
                }
            });
        }

        // 显示Toast消息
        showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000000;
                background: ${type === 'error' ? '#f56c6c' : type === 'success' ? '#67c23a' : '#409eff'};
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            toast.textContent = message;

            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 10);

            // 自动隐藏
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }

        // 清除缓存
        async clearCache(key) {
            return new Promise((resolve) => {
                const requestId = ++this.requestId;
                this.pendingRequests.set(requestId, resolve);

                window.postMessage({
                    type: 'INJECTED_CLEAR_CACHE',
                    requestId: requestId,
                    key: key
                }, '*');

                setTimeout(() => {
                    if (this.pendingRequests.has(requestId)) {
                        this.pendingRequests.delete(requestId);
                        resolve(false);
                    }
                }, 5000);
            });
        }
    }

    // 创建全局实例
    window.inspectionManager = new InspectionManager();

    console.log('[Injected] 检核管理器已初始化');
})();
