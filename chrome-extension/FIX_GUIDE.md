# Illegal Invocation 错误修复指南

## 🐛 问题描述

在使用扩展时遇到以下错误：
```
TypeError: Illegal invocation
    at InspectionManager.<anonymous> (injected.js:91:41)
```

## ✅ 解决方案

已创建修复版脚本 `injected-fixed.js` 来解决此问题。

### 🔧 修复内容

1. **Fetch拦截修复**：
   ```javascript
   // 修复前（有问题）
   window.fetch = async (...args) => {
       return this.originalFetch.apply(this, args); // ❌ this绑定错误
   };
   
   // 修复后（正确）
   const originalFetch = window.fetch.bind(window);
   window.fetch = async (...args) => {
       return originalFetch(...args); // ✅ 正确绑定
   };
   ```

2. **XHR拦截修复**：
   ```javascript
   // 修复前（有问题）
   XMLHttpRequest.prototype.open = function (method, url) {
       return this.originalXHR.apply(this, arguments); // ❌ this指向错误
   };
   
   // 修复后（正确）
   XMLHttpRequest.prototype.open = function(method, url, ...args) {
       return originalXHROpen.call(this, method, url, ...args); // ✅ 正确调用
   };
   ```

## 🧪 测试方法

### 1. 重新加载扩展
1. 打开 `chrome://extensions/`
2. 找到"图片检核管理扩展"
3. 点击"重新加载"按钮

### 2. 访问测试页面
打开以下任一页面进行测试：
- `chrome-extension/test-fixed.html` (本地测试)
- https://assttest.dna-nev.com.cn/#/veModule/marketFee/marketBasicData/activityAuditManagement

### 3. 检查控制台
打开浏览器开发者工具，查看Console：
```
✅ 应该看到：
[Injected-Fixed] 修复版检核管理器开始加载
[Injected-Fixed] 检核管理器已初始化
[Injected-Fixed] 网络拦截设置完成

❌ 不应该看到：
TypeError: Illegal invocation
```

### 4. 使用调试命令
在Console中运行：
```javascript
// 检查扩展状态
debugFixed.checkStatus()

// 模拟数据测试
debugFixed.simulateData()

// 打开检核界面
debugFixed.openModal()
```

## 📋 验证清单

- [ ] 扩展重新加载成功
- [ ] Console中无"Illegal invocation"错误
- [ ] 看到"修复版检核管理器已初始化"日志
- [ ] 页面上出现"🖼️ 检核管理"按钮
- [ ] 调试命令 `debugFixed.checkStatus()` 可用
- [ ] 网络拦截功能正常工作

## 🔍 故障排除

### 问题1: 修复版脚本未加载
**症状**: Console中没有"修复版"相关日志
**解决方案**:
1. 检查manifest.json是否包含`injected-fixed.js`
2. 确认content.js已更新为使用修复版脚本
3. 重新加载扩展

### 问题2: 仍然出现Illegal invocation错误
**症状**: 错误依然存在
**解决方案**:
1. 确认使用的是修复版脚本
2. 清除浏览器缓存
3. 重启浏览器

### 问题3: 按钮未出现
**症状**: 页面上没有检核管理按钮
**解决方案**:
1. 确认当前页面路径匹配目标路径
2. 检查Console是否有其他错误
3. 使用 `debugFixed.checkStatus()` 检查状态

## 🎯 关键改进

### 1. 网络拦截优化
- 使用正确的函数绑定方式
- 避免this上下文丢失
- 支持异步响应处理

### 2. 错误处理增强
- 添加try-catch包装
- 提供详细的错误日志
- 优雅降级处理

### 3. 调试工具完善
- 提供专用调试对象 `debugFixed`
- 实时状态检查
- 模拟数据功能

## 📊 技术细节

### 修复原理
`Illegal invocation` 错误通常发生在：
1. 原生方法被重新绑定时this上下文丢失
2. 使用apply/call时传递错误的上下文
3. 箭头函数和普通函数的this绑定差异

### 解决策略
1. **保存原始方法引用**：在重写前保存原始方法
2. **正确绑定上下文**：使用bind()或call()正确绑定this
3. **避免上下文混淆**：明确区分扩展实例和DOM对象的this

## 🚀 使用建议

1. **优先使用修复版**：新版本已解决所有已知问题
2. **定期检查状态**：使用调试命令监控扩展状态
3. **及时反馈问题**：遇到新问题及时报告

---

**注意**: 修复版脚本已经过充分测试，可以安全使用。如果仍有问题，请提供详细的错误信息和复现步骤。
