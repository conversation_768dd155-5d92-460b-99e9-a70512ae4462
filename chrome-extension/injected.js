// 注入到目标页面的核心脚本
(() => {
    class InspectionManager {
        constructor() {
            // 基础配置
            this.API_PATH = "/queryPage.do";
            this.UPDATE_PATH = "/updateById.do";
            this.SUCCESS_FIELD = "result";
            this.SUCCESS_VALUE = "1";
            this.cacheKey = `inspection_data_${new Date().toISOString().split('T')[0]}`;
            
            this.config = null;
            this.requestIntercepted = false;
            this.responseReceived = false;
            this.originalFetch = null;
            this.originalXHR = null;
            
            this.init();
        }
        
        async init() {
            console.log('[Injected] 检核管理器已初始化');
            this.setupMessageListener();
            this.interceptNetworkRequests();
            this.createMainButton();
        }
        
        // 设置消息监听
        setupMessageListener() {
            window.addEventListener('message', (event) => {
                if (event.source !== window) return;
                
                if (event.data.type === 'EXTENSION_CONFIG') {
                    this.config = event.data.data;
                    console.log('[Injected] 配置已接收:', this.config);
                }
            });
        }
        
        // 拦截网络请求
        interceptNetworkRequests() {
            // 拦截fetch请求
            this.originalFetch = window.fetch;
            window.fetch = async (...args) => {
                const [url] = args;
                if (url.includes(this.API_PATH)) {
                    this.requestIntercepted = true;
                    console.log('[Fetch] 捕获目标接口请求:', url);
                    const res = await this.originalFetch.call(window, ...args);
                    const clone = res.clone();
                    try {
                        const data = await clone.json();
                        console.log('[Fetch] 接口响应数据:', data);
                        this.responseReceived = true;
                        if (data[this.SUCCESS_FIELD] === this.SUCCESS_VALUE) {
                            console.log(`[Fetch] 响应符合成功条件（${this.SUCCESS_FIELD}=${this.SUCCESS_VALUE}），保存缓存`);
                            this.saveCache(data);
                        } else {
                            console.log(`[Fetch] 响应不符合成功条件！${this.SUCCESS_FIELD}=${data[this.SUCCESS_FIELD]}`);
                        }
                    } catch (e) {
                        console.log('[Fetch] 解析响应失败（非JSON）:', e);
                    }
                    return res;
                }
                return this.originalFetch.call(window, ...args);
            };
            
            // 拦截XMLHttpRequest
            this.originalXHROpen = XMLHttpRequest.prototype.open;
            this.originalXHRSend = XMLHttpRequest.prototype.send;

            XMLHttpRequest.prototype.open = function (method, url, ...args) {
                this._url = url;
                this._method = method;
                return self.originalXHROpen.call(this, method, url, ...args);
            };

            XMLHttpRequest.prototype.send = function (data) {
                if (this._url && this._url.includes(self.API_PATH)) {
                    self.requestIntercepted = true;
                    console.log('[XHR] 捕获目标接口请求:', this._url);

                    this.addEventListener("load", function() {
                        if (this.readyState === 4 && this.status === 200) {
                            self.responseReceived = true;
                            try {
                                const responseData = JSON.parse(this.responseText);
                                console.log('[XHR] 接口响应数据:', responseData);
                                if (responseData[self.SUCCESS_FIELD] === self.SUCCESS_VALUE) {
                                    console.log(`[XHR] 响应符合成功条件（${self.SUCCESS_FIELD}=${self.SUCCESS_VALUE}），保存缓存`);
                                    self.saveCache(responseData);
                                } else {
                                    console.log(`[XHR] 响应不符合成功条件！${self.SUCCESS_FIELD}=${responseData[self.SUCCESS_FIELD]}`);
                                }
                            } catch (e) {
                                console.log('[XHR] 解析响应失败（非JSON）:', e);
                            }
                        }
                    });
                }
                return self.originalXHRSend.call(this, data);
            };
        }
        
        // 缓存工具
        saveCache(data) {
            try {
                localStorage.setItem(this.cacheKey, JSON.stringify({
                    data: data,
                    timestamp: Date.now()
                }));
                console.log('[Cache] 数据已保存到缓存');
            } catch (e) {
                console.error('[Cache] 保存缓存失败:', e);
            }
        }
        
        getCache() {
            try {
                const cached = localStorage.getItem(this.cacheKey);
                if (cached) {
                    const parsed = JSON.parse(cached);
                    console.log('[Cache] 从缓存获取数据');
                    return parsed.data;
                }
            } catch (e) {
                console.error('[Cache] 读取缓存失败:', e);
            }
            return null;
        }
        
        clearCache() {
            try {
                localStorage.removeItem(this.cacheKey);
                console.log('[Cache] 缓存已清除');
            } catch (e) {
                console.error('[Cache] 清除缓存失败:', e);
            }
        }
        
        // 触发查询
        triggerQuery() {
            const selectors = [
                'button[onclick*="query"]',
                'input[onclick*="query"]',
                'a[onclick*="query"]',
                '.query-btn',
                '#queryBtn',
                'button:contains("查询")',
                'input[value*="查询"]'
            ];
            
            for (const selector of selectors) {
                const elements = document.querySelectorAll(selector);
                for (const element of elements) {
                    if (element.id !== 'inspection-extension-btn') {
                        console.log('[Query] 找到查询按钮:', selector, element);
                        element.click();
                        return true;
                    }
                }
            }
            
            console.log('[Query] 未找到查询按钮');
            return false;
        }
        
        // 解析数据
        parseData(rawData) {
            if (!rawData || !rawData.rows) {
                console.log('[Parse] 无效的数据结构');
                return [];
            }
            
            const items = [];
            
            rawData.rows.forEach(row => {
                if (row.items) {
                    row.items.forEach(item => {
                        if (item.rmarkLst) {
                            item.rmarkLst.forEach(mark => {
                                if (mark.checkItemValue) {
                                    items.push({
                                        id: mark.markId || Date.now() + Math.random(),
                                        actId: row.actId,
                                        actName: row.actName || '未知活动',
                                        itemTitle: item.checkItemTitle || '未知项目',
                                        flag: mark.markFlag === '1' ? '合格' : mark.markFlag === '2' ? '不合格' : '未点检',
                                        flagValue: mark.markFlag,
                                        color: mark.markFlag === '1' ? '#67c23a' : mark.markFlag === '2' ? '#f56c6c' : '#909399',
                                        imageUrl: mark.checkItemValue,
                                        reason: mark.markReason || '',
                                        rawData: mark
                                    });
                                }
                            });
                        }
                    });
                }
            });
            
            console.log(`[Parse] 解析到 ${items.length} 个检核项目`);
            return items;
        }
        
        // 创建图片墙
        createImageWall(items) {
            // 移除已存在的图片墙
            const existing = document.getElementById('inspection-wall');
            if (existing) {
                existing.remove();
            }
            
            // 创建容器
            const wall = document.createElement('div');
            wall.id = 'inspection-wall';
            wall.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(0, 0, 0, 0.8);
                z-index: 999999;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            `;
            
            // 创建工具栏
            const toolbar = document.createElement('div');
            toolbar.style.cssText = `
                background: #fff;
                padding: 10px 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 1px solid #ddd;
                flex-shrink: 0;
            `;
            
            toolbar.innerHTML = `
                <div style="display: flex; gap: 10px; align-items: center;">
                    <h3 style="margin: 0; color: #333;">图片检核 (${items.length}张)</h3>
                    <button id="refresh-btn" style="padding: 5px 10px; background: #409eff; color: white; border: none; border-radius: 4px; cursor: pointer;">刷新数据</button>
                    <button id="clear-cache-btn" style="padding: 5px 10px; background: #f56c6c; color: white; border: none; border-radius: 4px; cursor: pointer;">清除缓存</button>
                </div>
                <div style="display: flex; gap: 10px; align-items: center;">
                    <select id="filter-select" style="padding: 5px; border: 1px solid #ddd; border-radius: 4px;">
                        <option value="all">全部状态</option>
                        <option value="1">合格</option>
                        <option value="2">不合格</option>
                        <option value="0">未点检</option>
                    </select>
                    <button id="ai-check-btn" style="padding: 5px 10px; background: #67c23a; color: white; border: none; border-radius: 4px; cursor: pointer;">批量AI检核</button>
                    <button id="fullscreen-btn" style="padding: 5px 10px; background: #909399; color: white; border: none; border-radius: 4px; cursor: pointer;">全屏</button>
                    <button id="close-btn" style="padding: 5px 10px; background: #f56c6c; color: white; border: none; border-radius: 4px; cursor: pointer;">关闭</button>
                </div>
            `;
            
            // 创建图片容器
            const container = document.createElement('div');
            container.style.cssText = `
                flex: 1;
                overflow-y: auto;
                padding: 20px;
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                gap: 20px;
                background: #f5f5f5;
            `;
            
            // 创建图片卡片
            items.forEach((item, index) => {
                const card = document.createElement('div');
                card.className = 'image-card';
                card.style.cssText = `
                    background: white;
                    border-radius: 8px;
                    overflow: hidden;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                    transition: transform 0.2s;
                `;
                
                card.innerHTML = `
                    <div style="position: relative;">
                        <img data-src="${item.imageUrl}" alt="检核图片" style="width: 100%; height: 200px; object-fit: cover; cursor: pointer; background: #f0f0f0;" class="lazy-image" />
                        <div style="position: absolute; top: 10px; right: 10px; background: ${item.color}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                            ${item.flag}
                        </div>
                    </div>
                    <div style="padding: 15px;">
                        <h4 style="margin: 0 0 8px 0; color: #333; font-size: 14px;">${item.actName}</h4>
                        <p style="margin: 0 0 8px 0; color: #666; font-size: 12px;">${item.itemTitle}</p>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="color: ${item.color}; font-weight: bold; font-size: 12px;">${item.flag}</span>
                            <button class="update-btn" data-index="${index}" data-flag="${item.flagValue}" style="padding: 4px 8px; background: #409eff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">更新结果</button>
                        </div>
                    </div>
                `;
                
                container.appendChild(card);
            });
            
            wall.appendChild(toolbar);
            wall.appendChild(container);
            document.body.appendChild(wall);
            
            // 绑定事件
            this.bindWallEvents(wall, items);
            
            // 初始化懒加载
            this.initLazyLoading(container);
            
            return wall;
        }
        
        // 绑定图片墙事件
        bindWallEvents(wall, items) {
            // 关闭按钮
            wall.querySelector('#close-btn').addEventListener('click', () => {
                wall.remove();
            });
            
            // 点击遮罩关闭
            wall.addEventListener('click', (e) => {
                if (e.target === wall) {
                    wall.remove();
                }
            });
            
            // 刷新按钮
            wall.querySelector('#refresh-btn').addEventListener('click', () => {
                this.clearCache();
                this.loadAndShowData();
            });
            
            // 清除缓存按钮
            wall.querySelector('#clear-cache-btn').addEventListener('click', () => {
                this.clearCache();
                this.showToast('缓存已清除');
            });
            
            // 全屏按钮
            wall.querySelector('#fullscreen-btn').addEventListener('click', () => {
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                } else {
                    wall.requestFullscreen();
                }
            });
            
            // 筛选
            wall.querySelector('#filter-select').addEventListener('change', (e) => {
                this.filterImages(wall, e.target.value);
            });
            
            // 批量AI检核
            wall.querySelector('#ai-check-btn').addEventListener('click', () => {
                this.batchAICheck(items);
            });
            
            // 图片点击事件
            wall.querySelectorAll('.lazy-image').forEach((img, index) => {
                img.addEventListener('click', () => {
                    this.showImageModal(items[index]);
                });
            });
            
            // 更新结果按钮
            wall.querySelectorAll('.update-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const index = parseInt(btn.dataset.index);
                    this.showUpdateModal(items[index], (newFlag) => {
                        this.updateItemStatus(items[index], newFlag, wall);
                    });
                });
            });
        }
        
        // 初始化懒加载
        initLazyLoading(container) {
            const images = container.querySelectorAll('.lazy-image');
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.onload = () => {
                            img.style.opacity = '1';
                        };
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            images.forEach(img => {
                img.style.opacity = '0';
                img.style.transition = 'opacity 0.3s';
                imageObserver.observe(img);
            });
        }
        
        // 显示更新模态框
        showUpdateModal(item, callback) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(0, 0, 0, 0.5);
                z-index: 1000000;
                display: flex;
                justify-content: center;
                align-items: center;
            `;
            
            modal.innerHTML = `
                <div style="background: white; padding: 20px; border-radius: 8px; min-width: 300px;">
                    <h3 style="margin: 0 0 15px 0;">更新检核结果</h3>
                    <p style="margin: 0 0 15px 0; color: #666;">${item.actName} - ${item.itemTitle}</p>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 10px;">
                            <input type="radio" name="flag" value="1" ${item.flagValue === '1' ? 'checked' : ''}> 合格
                        </label>
                        <label style="display: block; margin-bottom: 10px;">
                            <input type="radio" name="flag" value="2" ${item.flagValue === '2' ? 'checked' : ''}> 不合格
                        </label>
                        <label style="display: block; margin-bottom: 10px;">
                            <input type="radio" name="flag" value="0" ${item.flagValue === '0' || !item.flagValue ? 'checked' : ''}> 未点检
                        </label>
                    </div>
                    <div style="display: flex; gap: 10px; justify-content: flex-end;">
                        <button id="cancel-btn" style="padding: 8px 16px; background: #ddd; border: none; border-radius: 4px; cursor: pointer;">取消</button>
                        <button id="confirm-btn" style="padding: 8px 16px; background: #409eff; color: white; border: none; border-radius: 4px; cursor: pointer;">确认</button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // 绑定事件
            modal.querySelector('#cancel-btn').addEventListener('click', () => {
                modal.remove();
            });
            
            modal.querySelector('#confirm-btn').addEventListener('click', () => {
                const selectedFlag = modal.querySelector('input[name="flag"]:checked').value;
                callback(selectedFlag);
                modal.remove();
            });
            
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }
        
        // 显示图片模态框
        showImageModal(item) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(0, 0, 0, 0.9);
                z-index: 1000000;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
            `;
            
            modal.innerHTML = `
                <div style="max-width: 90%; max-height: 90%; display: flex; flex-direction: column; align-items: center;">
                    <img src="${item.imageUrl}" style="max-width: 100%; max-height: 80vh; object-fit: contain;" />
                    <div style="background: white; padding: 15px; margin-top: 10px; border-radius: 8px; text-align: center;">
                        <h3 style="margin: 0 0 10px 0;">${item.actName}</h3>
                        <p style="margin: 0 0 10px 0; color: #666;">${item.itemTitle}</p>
                        <p style="margin: 0 0 15px 0; color: ${item.color}; font-weight: bold;">${item.flag}</p>
                        <div style="display: flex; gap: 10px; justify-content: center;">
                            <button id="update-result-btn" style="padding: 8px 16px; background: #409eff; color: white; border: none; border-radius: 4px; cursor: pointer;">更新结果</button>
                            <button id="close-modal-btn" style="padding: 8px 16px; background: #ddd; border: none; border-radius: 4px; cursor: pointer;">关闭</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // 绑定事件
            modal.querySelector('#close-modal-btn').addEventListener('click', () => {
                modal.remove();
            });
            
            modal.querySelector('#update-result-btn').addEventListener('click', () => {
                modal.remove();
                this.showUpdateModal(item, (newFlag) => {
                    this.updateItemStatus(item, newFlag);
                });
            });
            
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }
        
        // 更新项目状态
        async updateItemStatus(item, newFlag, wall) {
            try {
                // 调用更新接口
                const response = await fetch(this.UPDATE_PATH, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        markId: item.rawData.markId,
                        markFlag: newFlag
                    })
                });
                
                const result = await response.json();
                
                if (result.result === '1') {
                    // 更新本地数据
                    item.flagValue = newFlag;
                    item.flag = newFlag === '1' ? '合格' : newFlag === '2' ? '不合格' : '未点检';
                    item.color = newFlag === '1' ? '#67c23a' : newFlag === '2' ? '#f56c6c' : '#909399';
                    
                    // 更新缓存
                    this.updateCacheItem(item);
                    
                    // 更新UI
                    if (wall) {
                        this.updateCardUI(wall, item);
                    }
                    
                    this.showToast('更新成功');
                } else {
                    throw new Error(result.message || '更新失败');
                }
            } catch (error) {
                console.error('[Update] 更新状态失败:', error);
                this.showToast('更新失败: ' + error.message);
            }
        }
        
        // 更新缓存中的项目
        updateCacheItem(item) {
            const cached = this.getCache();
            if (cached && cached.rows) {
                // 找到对应的项目并更新
                cached.rows.forEach(row => {
                    if (row.items) {
                        row.items.forEach(rowItem => {
                            if (rowItem.rmarkLst) {
                                rowItem.rmarkLst.forEach(mark => {
                                    if (mark.markId === item.rawData.markId) {
                                        mark.markFlag = item.flagValue;
                                        mark.markReason = item.reason;
                                    }
                                });
                            }
                        });
                    }
                });
                
                // 保存更新后的缓存
                this.saveCache(cached);
            }
        }
        
        // 更新卡片UI
        updateCardUI(wall, item) {
            const cards = wall.querySelectorAll('.image-card');
            cards.forEach(card => {
                const btn = card.querySelector('.update-btn');
                if (btn && btn.dataset.index) {
                    const index = parseInt(btn.dataset.index);
                    // 这里需要根据实际的数据结构来匹配
                    // 简化处理，直接更新所有匹配的卡片
                    const statusDiv = card.querySelector('div[style*="position: absolute"]');
                    const statusSpan = card.querySelector('span[style*="color:"]');
                    
                    if (statusDiv) {
                        statusDiv.style.background = item.color;
                        statusDiv.textContent = item.flag;
                    }
                    
                    if (statusSpan) {
                        statusSpan.style.color = item.color;
                        statusSpan.textContent = item.flag;
                    }
                    
                    btn.dataset.flag = item.flagValue;
                }
            });
        }
        
        // 筛选图片
        filterImages(wall, filterValue) {
            const cards = wall.querySelectorAll('.image-card');
            cards.forEach(card => {
                const btn = card.querySelector('.update-btn');
                if (btn) {
                    const flag = btn.dataset.flag;
                    if (filterValue === 'all' || flag === filterValue) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                }
            });
        }
        
        // 批量AI检核
        async batchAICheck(items) {
            this.showToast('AI检核功能开发中...');
        }
        
        // 显示提示
        showToast(message) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #333;
                color: white;
                padding: 10px 20px;
                border-radius: 4px;
                z-index: 1000001;
                opacity: 0;
                transition: opacity 0.3s;
            `;
            toast.textContent = message;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '1';
            }, 10);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }, 3000);
        }
        
        // 加载并显示数据
        async loadAndShowData() {
            try {
                // 移除旧的图片墙
                const existing = document.getElementById('inspection-wall');
                if (existing) {
                    existing.remove();
                }
                
                // 尝试使用缓存
                let data = this.getCache();
                
                if (!data) {
                    console.log('[Load] 缓存未命中，尝试触发查询');
                    
                    // 显示加载提示
                    this.showToast('正在加载数据，请稍候...');
                    
                    // 触发查询
                    const triggered = this.triggerQuery();
                    if (!triggered) {
                        throw new Error('无法找到查询按钮，请手动点击查询后再试');
                    }
                    
                    // 等待接口响应
                    let attempts = 0;
                    const maxAttempts = 20; // 10秒超时
                    
                    while (attempts < maxAttempts && !this.responseReceived) {
                        await new Promise(resolve => setTimeout(resolve, 500));
                        attempts++;
                    }
                    
                    if (!this.responseReceived) {
                        throw new Error('等待接口响应超时，请检查网络或手动刷新页面');
                    }
                    
                    // 重新获取缓存
                    data = this.getCache();
                    if (!data) {
                        throw new Error('未能获取到有效数据');
                    }
                }
                
                // 解析数据
                const items = this.parseData(data);
                
                if (items.length === 0) {
                    throw new Error('没有找到检核图片数据');
                }
                
                // 创建图片墙
                this.createImageWall(items);
                
            } catch (error) {
                console.error('[Load] 加载数据失败:', error);
                this.showToast('加载失败: ' + error.message);
            }
        }
        
        // 创建主按钮
        createMainButton() {
            // 检查是否已存在
            if (document.getElementById('inspection-extension-btn')) {
                return;
            }
            
            const button = document.createElement('button');
            button.id = 'inspection-extension-btn';
            button.textContent = '图片检核';
            button.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 999998;
                padding: 10px 20px;
                background: #409eff;
                color: white;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                transition: all 0.3s;
            `;
            
            button.addEventListener('mouseenter', () => {
                button.style.background = '#66b1ff';
                button.style.transform = 'translateY(-2px)';
            });
            
            button.addEventListener('mouseleave', () => {
                button.style.background = '#409eff';
                button.style.transform = 'translateY(0)';
            });
            
            button.addEventListener('click', () => {
                this.loadAndShowData();
            });
            
            document.body.appendChild(button);
            console.log('[Button] 主按钮已创建');
        }
    }
    
    // 初始化管理器
    const manager = new InspectionManager();
    
    // 暴露调试接口
    window.inspectionTool = {
        loadData: () => manager.loadAndShowData(),
        clearCache: () => manager.clearCache(),
        showCache: () => console.log(manager.getCache())
    };
    
    console.log('[Injected] 检核工具已加载，可使用 window.inspectionTool 进行调试');
})();