<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="icon-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆角矩形 -->
  <rect width="128" height="128" rx="24" ry="24" fill="url(#bg-gradient)"/>
  
  <!-- 主图标 - 图片框架 -->
  <rect x="24" y="32" width="80" height="64" rx="8" ry="8" fill="url(#icon-gradient)" stroke="#667eea" stroke-width="2"/>
  
  <!-- 图片内容 - 山峰 -->
  <path d="M32 80 L48 64 L64 72 L80 56 L96 64 L96 88 L32 88 Z" fill="#667eea" opacity="0.6"/>
  
  <!-- 太阳 -->
  <circle cx="80" cy="48" r="8" fill="#fbbf24"/>
  
  <!-- 检核标记 - 对勾 -->
  <circle cx="88" cy="40" r="12" fill="#10b981"/>
  <path d="M82 40 L86 44 L94 36" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
  
  <!-- AI标识 -->
  <rect x="28" y="36" width="20" height="12" rx="6" ry="6" fill="#06b6d4"/>
  <text x="38" y="44" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="8" font-weight="bold">AI</text>
  
  <!-- 底部装饰线 -->
  <rect x="24" y="104" width="80" height="4" rx="2" ry="2" fill="url(#icon-gradient)" opacity="0.8"/>
</svg>
