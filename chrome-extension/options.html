<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>检核图片管理扩展 - 配置</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .header h1 {
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
        }
        
        .config-section {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .config-section h2 {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .form-group small {
            display: block;
            margin-top: 5px;
            color: #666;
            font-size: 12px;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border: 1px solid transparent;
        }
        
        .alert-success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .alert-error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .alert-info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-enabled {
            background: #28a745;
        }
        
        .status-disabled {
            background: #dc3545;
        }
        
        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 12px 24px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .tab.active {
            color: #007bff;
            border-bottom-color: #007bff;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .list-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 4px;
            margin-bottom: 10px;
            background: #f9f9f9;
        }
        
        .list-item:last-child {
            margin-bottom: 0;
        }
        
        .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .add-item {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        
        .add-item input {
            flex: 1;
        }
        
        .add-item button {
            padding: 8px 16px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 检核图片管理扩展配置</h1>
            <p>配置扩展的各项参数以适应您的使用需求</p>
        </div>
        
        <div id="alert-container"></div>
        
        <div class="config-section">
            <div class="tabs">
                <button class="tab active" data-tab="basic">基础配置</button>
                <button class="tab" data-tab="api">接口配置</button>
                <button class="tab" data-tab="ai">AI配置</button>
                <button class="tab" data-tab="cache">缓存配置</button>
                <button class="tab" data-tab="advanced">高级配置</button>
            </div>
            
            <!-- 基础配置 -->
            <div class="tab-content active" id="basic">
                <h2>基础配置</h2>
                
                <div class="form-group">
                    <label>目标页面域名</label>
                    <div id="target-pages-list"></div>
                    <div class="add-item">
                        <input type="text" id="new-target-page" placeholder="例如: assttest.dna-nev.com.cn">
                        <button onclick="addTargetPage()">添加</button>
                    </div>
                    <small>扩展将在这些域名的页面上激活</small>
                </div>
                
                <div class="form-group">
                    <label>查询按钮选择器</label>
                    <div id="button-selectors-list"></div>
                    <div class="add-item">
                        <input type="text" id="new-button-selector" placeholder="例如: .search-btn">
                        <button onclick="addButtonSelector()">添加</button>
                    </div>
                    <small>用于自动触发查询的按钮CSS选择器</small>
                </div>
            </div>
            
            <!-- 接口配置 -->
            <div class="tab-content" id="api">
                <h2>接口配置</h2>
                
                <div class="form-group">
                    <label for="query-endpoint">查询接口路径</label>
                    <input type="text" id="query-endpoint" placeholder="/ly/mp/busicen/acc/accCheckInfo/queryPage.do">
                    <small>用于获取检核数据的接口路径</small>
                </div>
                
                <div class="form-group">
                    <label for="update-endpoint">更新接口路径</label>
                    <input type="text" id="update-endpoint" placeholder="/ly/mp/busicen/acc/accCheckRecordInfo/updateById.do">
                    <small>用于更新检核状态的接口路径</small>
                </div>
                
                <div class="form-group">
                    <label for="request-timeout">请求超时时间 (毫秒)</label>
                    <input type="number" id="request-timeout" min="1000" max="60000" value="30000">
                    <small>API请求的超时时间</small>
                </div>
            </div>
            
            <!-- AI配置 -->
            <div class="tab-content" id="ai">
                <h2>AI检核配置</h2>
                
                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="ai-enabled">
                        <label for="ai-enabled">启用AI检核功能</label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="ai-api-url">AI接口地址</label>
                    <input type="url" id="ai-api-url" placeholder="https://api.example.com/ai/inspect">
                    <small>AI检核服务的API地址</small>
                </div>
                
                <div class="form-group">
                    <label for="ai-api-key">AI接口密钥</label>
                    <input type="password" id="ai-api-key" placeholder="输入您的API密钥">
                    <small>用于访问AI服务的密钥</small>
                </div>
                
                <div class="form-group">
                    <label for="ai-confidence-threshold">可信度阈值 (%)</label>
                    <input type="number" id="ai-confidence-threshold" min="0" max="100" value="60">
                    <small>低于此阈值的AI结果将被标记为低可信度</small>
                </div>
                
                <div class="form-group">
                    <button class="btn btn-secondary" onclick="testAIConnection()">测试AI连接</button>
                </div>
            </div>
            
            <!-- 缓存配置 -->
            <div class="tab-content" id="cache">
                <h2>缓存配置</h2>
                
                <div class="form-group">
                    <label for="cache-expire-time">默认缓存过期时间 (分钟)</label>
                    <input type="number" id="cache-expire-time" min="1" max="1440" value="30">
                    <small>缓存数据的默认过期时间</small>
                </div>
                
                <div class="form-group">
                    <label for="max-cache-size">最大缓存大小 (MB)</label>
                    <input type="number" id="max-cache-size" min="10" max="500" value="100">
                    <small>扩展可使用的最大缓存空间</small>
                </div>
                
                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="auto-clear-cache">
                        <label for="auto-clear-cache">自动清理过期缓存</label>
                    </div>
                    <small>启用后将定期清理过期的缓存数据</small>
                </div>
                
                <div class="form-group">
                    <button class="btn btn-danger" onclick="clearAllCache()">清除所有缓存</button>
                    <small style="margin-top: 10px; display: block;">⚠️ 此操作将删除所有已缓存的数据</small>
                </div>
            </div>
            
            <!-- 高级配置 -->
            <div class="tab-content" id="advanced">
                <h2>高级配置</h2>
                
                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="debug-mode">
                        <label for="debug-mode">启用调试模式</label>
                    </div>
                    <small>启用后将输出详细的调试信息</small>
                </div>
                
                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="auto-inject">
                        <label for="auto-inject">自动注入脚本</label>
                    </div>
                    <small>在匹配的页面上自动注入检核管理脚本</small>
                </div>
                
                <div class="form-group">
                    <label for="injection-delay">注入延迟 (毫秒)</label>
                    <input type="number" id="injection-delay" min="0" max="10000" value="1000">
                    <small>页面加载后延迟多久注入脚本</small>
                </div>
                
                <div class="form-group">
                    <label for="max-concurrent-requests">最大并发请求数</label>
                    <input type="number" id="max-concurrent-requests" min="1" max="10" value="3">
                    <small>同时进行的最大API请求数量</small>
                </div>
                
                <div class="form-group">
                    <button class="btn btn-secondary" onclick="exportConfig()">导出配置</button>
                    <button class="btn btn-secondary" onclick="importConfig()">导入配置</button>
                    <input type="file" id="config-file" accept=".json" style="display: none;" onchange="handleConfigImport(event)">
                </div>
            </div>
        </div>
        
        <div class="btn-group">
            <button class="btn btn-primary" onclick="saveConfig()">💾 保存配置</button>
            <button class="btn btn-secondary" onclick="resetConfig()">🔄 重置为默认</button>
            <button class="btn btn-secondary" onclick="loadConfig()">📥 重新加载</button>
        </div>
        
        <div class="config-section">
            <h2>扩展状态</h2>
            <div class="form-group">
                <div style="display: flex; align-items: center;">
                    <span class="status-indicator" id="extension-status"></span>
                    <span id="extension-status-text">检查中...</span>
                </div>
            </div>
            
            <div class="form-group">
                <label>版本信息</label>
                <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px;">
                    扩展版本: <span id="extension-version">1.0.0</span><br>
                    最后更新: <span id="last-updated">-</span>
                </div>
            </div>
        </div>
    </div>
    
    <script src="options.js"></script>
</body>
</html>