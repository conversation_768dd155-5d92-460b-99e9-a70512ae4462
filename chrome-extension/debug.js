// 调试脚本 - 用于测试扩展功能
(() => {
    'use strict';
    
    console.log('🔧 [Debug] 调试脚本已加载');
    
    // 调试工具类
    class ExtensionDebugger {
        constructor() {
            this.init();
        }
        
        init() {
            console.log('🔧 [Debug] 初始化调试工具');
            this.checkEnvironment();
            this.setupDebugCommands();
            this.monitorExtension();
        }
        
        // 检查环境
        checkEnvironment() {
            console.log('🔧 [Debug] 环境检查:');
            console.log('  - URL:', window.location.href);
            console.log('  - Hash:', window.location.hash);
            console.log('  - Host:', window.location.hostname);
            console.log('  - Path:', window.location.pathname);
            
            // 检查是否为目标页面
            const targetPaths = [
                '/veModule/marketFee/marketBasicData/activityAuditManagement',
                '/veModule/marketFee/marketBasicData/recordViewing'
            ];
            const isTargetPage = targetPaths.some(path => window.location.hash.includes(path));
            console.log('  - 是否为目标页面:', isTargetPage);
            
            // 提取actId
            const urlParams = new URLSearchParams(window.location.search);
            const hashParams = new URLSearchParams(window.location.hash.split('?')[1] || '');
            const actId = urlParams.get('actId') || hashParams.get('actId');
            console.log('  - ActId:', actId);
        }
        
        // 设置调试命令
        setupDebugCommands() {
            // 全局调试对象
            window.extensionDebug = {
                // 检查扩展状态
                checkExtension: () => {
                    console.log('🔧 [Debug] 检查扩展状态:');
                    console.log('  - inspectionManager:', !!window.inspectionManager);
                    console.log('  - contentScript:', !!window.contentScript);
                    
                    if (window.inspectionManager) {
                        console.log('  - API_PATH:', window.inspectionManager.API_PATH);
                        console.log('  - currentActId:', window.inspectionManager.currentActId);
                        console.log('  - cacheKey:', window.inspectionManager.cacheKey);
                    }
                },
                
                // 模拟API请求
                simulateAPI: () => {
                    console.log('🔧 [Debug] 模拟API请求');
                    const mockData = {
                        result: "1",
                        rows: [
                            {
                                actId: "test_act_001",
                                actName: "测试活动",
                                items: [
                                    {
                                        checkItemTitle: "外观检查",
                                        rmarkLst: [
                                            {
                                                markId: "mark_001",
                                                markFlag: "1",
                                                markReason: "",
                                                checkItemValue: "https://picsum.photos/400/300?random=1"
                                            },
                                            {
                                                markId: "mark_002",
                                                markFlag: "2", 
                                                markReason: "质量问题",
                                                checkItemValue: "https://picsum.photos/400/300?random=2"
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    };
                    
                    if (window.inspectionManager) {
                        window.inspectionManager.saveCache(mockData);
                        console.log('🔧 [Debug] 模拟数据已保存到缓存');
                    }
                },
                
                // 打开检核界面
                openModal: () => {
                    console.log('🔧 [Debug] 尝试打开检核界面');
                    if (window.inspectionManager) {
                        window.inspectionManager.openInspectionModal();
                    } else {
                        console.error('🔧 [Debug] inspectionManager 未找到');
                    }
                },
                
                // 检查缓存
                checkCache: () => {
                    console.log('🔧 [Debug] 检查缓存:');
                    if (window.inspectionManager) {
                        const cached = window.inspectionManager.getCache();
                        console.log('  - 缓存数据:', cached);
                    }
                    
                    // 检查localStorage
                    const keys = Object.keys(localStorage).filter(key => key.includes('inspection'));
                    console.log('  - localStorage keys:', keys);
                    keys.forEach(key => {
                        console.log(`  - ${key}:`, localStorage.getItem(key));
                    });
                },
                
                // 清除缓存
                clearCache: () => {
                    console.log('🔧 [Debug] 清除缓存');
                    const keys = Object.keys(localStorage).filter(key => key.includes('inspection'));
                    keys.forEach(key => localStorage.removeItem(key));
                    console.log('🔧 [Debug] 缓存已清除');
                },
                
                // 测试网络拦截
                testIntercept: () => {
                    console.log('🔧 [Debug] 测试网络拦截');
                    
                    // 模拟fetch请求
                    fetch('/ly/mp/busicen/acc/accCheckInfo/queryPage.do', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ test: true })
                    }).then(response => {
                        console.log('🔧 [Debug] Fetch请求已发送');
                    }).catch(error => {
                        console.log('🔧 [Debug] Fetch请求失败:', error);
                    });
                    
                    // 模拟XHR请求
                    const xhr = new XMLHttpRequest();
                    xhr.open('POST', '/ly/mp/busicen/acc/accCheckInfo/queryPage.do');
                    xhr.setRequestHeader('Content-Type', 'application/json');
                    xhr.onload = () => {
                        console.log('🔧 [Debug] XHR请求完成');
                    };
                    xhr.send(JSON.stringify({ test: true }));
                },
                
                // 显示帮助
                help: () => {
                    console.log('🔧 [Debug] 可用命令:');
                    console.log('  - extensionDebug.checkExtension() - 检查扩展状态');
                    console.log('  - extensionDebug.simulateAPI() - 模拟API数据');
                    console.log('  - extensionDebug.openModal() - 打开检核界面');
                    console.log('  - extensionDebug.checkCache() - 检查缓存');
                    console.log('  - extensionDebug.clearCache() - 清除缓存');
                    console.log('  - extensionDebug.testIntercept() - 测试网络拦截');
                    console.log('  - extensionDebug.help() - 显示帮助');
                }
            };
            
            console.log('🔧 [Debug] 调试命令已设置，使用 extensionDebug.help() 查看可用命令');
        }
        
        // 监控扩展
        monitorExtension() {
            // 监控扩展加载
            const checkExtension = () => {
                if (window.inspectionManager) {
                    console.log('🔧 [Debug] 检测到 inspectionManager');
                    clearInterval(checkInterval);
                    
                    // 监控扩展状态变化
                    this.monitorExtensionState();
                }
            };
            
            const checkInterval = setInterval(checkExtension, 1000);
            
            // 10秒后停止检查
            setTimeout(() => {
                clearInterval(checkInterval);
                if (!window.inspectionManager) {
                    console.warn('🔧 [Debug] 10秒内未检测到扩展，请检查注入状态');
                }
            }, 10000);
        }
        
        // 监控扩展状态
        monitorExtensionState() {
            if (!window.inspectionManager) return;
            
            const manager = window.inspectionManager;
            
            // 监控请求拦截状态
            setInterval(() => {
                if (manager.requestIntercepted && !this.lastRequestState) {
                    console.log('🔧 [Debug] 检测到请求拦截');
                    this.lastRequestState = true;
                }
                
                if (manager.responseReceived && !this.lastResponseState) {
                    console.log('🔧 [Debug] 检测到响应接收');
                    this.lastResponseState = true;
                }
            }, 1000);
        }
    }
    
    // 初始化调试器
    const debugger = new ExtensionDebugger();
    
    // 页面加载完成后的额外检查
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                console.log('🔧 [Debug] 页面加载完成，执行最终检查');
                debugger.checkEnvironment();
            }, 2000);
        });
    } else {
        setTimeout(() => {
            console.log('🔧 [Debug] 页面已加载，执行检查');
            debugger.checkEnvironment();
        }, 2000);
    }
    
})();
