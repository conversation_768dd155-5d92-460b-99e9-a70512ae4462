// 弹出页面JavaScript逻辑
class PopupManager {
    constructor() {
        this.currentTab = null;
        this.config = null;
        this.init();
    }
    
    async init() {
        try {
            await this.loadCurrentTab();
            await this.loadConfig();
            await this.updateStatus();
            await this.updateStats();
            this.setupEventListeners();
            this.log('弹出页面初始化完成');
        } catch (error) {
            this.showAlert('初始化失败: ' + error.message, 'error');
            this.log('弹出页面初始化失败', error);
        }
    }
    
    async loadCurrentTab() {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        this.currentTab = tab;
        
        const urlElement = document.getElementById('current-url');
        if (tab && tab.url) {
            urlElement.textContent = tab.url;
        } else {
            urlElement.textContent = '无法获取当前页面';
        }
    }
    
    async loadConfig() {
        try {
            const result = await chrome.storage.sync.get('extensionConfig');
            this.config = result.extensionConfig || {};
        } catch (error) {
            this.log('配置加载失败', error);
            this.config = {};
        }
    }
    
    async updateStatus() {
        // 更新扩展状态
        this.setStatus('extension-status', 'extension-status-text', true, '运行正常');
        
        // 检查页面匹配状态
        const isPageMatch = this.checkPageMatch();
        this.setStatus('page-match-status', 'page-match-text', isPageMatch, 
            isPageMatch ? '页面匹配' : '页面不匹配');
        
        // 检查脚本注入状态
        await this.checkInjectionStatus();
        
        // 检查AI功能状态
        const aiEnabled = this.config.aiEnabled && this.config.aiApiUrl && this.config.aiApiKey;
        this.setStatus('ai-status', 'ai-text', aiEnabled, 
            aiEnabled ? 'AI已启用' : 'AI未配置');
        
        // 更新按钮状态
        this.updateButtonStates(isPageMatch);
    }
    
    checkPageMatch() {
        if (!this.currentTab || !this.currentTab.url || !this.config.targetPages) {
            return false;
        }
        
        const currentUrl = new URL(this.currentTab.url);
        return this.config.targetPages.some(targetPage => 
            currentUrl.hostname.includes(targetPage)
        );
    }
    
    async checkInjectionStatus() {
        if (!this.currentTab || !this.checkPageMatch()) {
            this.setStatus('injection-status', 'injection-text', false, '页面不匹配');
            return;
        }
        
        try {
            // 尝试向内容脚本发送消息检查注入状态
            const response = await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'checkInjectionStatus'
            });
            
            if (response && response.injected) {
                this.setStatus('injection-status', 'injection-text', true, '已注入');
            } else {
                this.setStatus('injection-status', 'injection-text', false, '未注入');
            }
        } catch (error) {
            this.setStatus('injection-status', 'injection-text', false, '检查失败');
        }
    }
    
    setStatus(indicatorId, textId, isActive, text) {
        const indicator = document.getElementById(indicatorId);
        const textElement = document.getElementById(textId);
        
        if (indicator) {
            indicator.className = `status-indicator ${
                isActive ? 'status-active' : 'status-inactive'
            }`;
        }
        
        if (textElement) {
            textElement.textContent = text;
        }
    }
    
    updateButtonStates(isPageMatch) {
        const injectBtn = document.getElementById('inject-btn');
        const inspectorBtn = document.getElementById('open-inspector');
        
        if (isPageMatch) {
            injectBtn.classList.remove('btn-disabled');
            inspectorBtn.classList.remove('btn-disabled');
            injectBtn.disabled = false;
            inspectorBtn.disabled = false;
        } else {
            injectBtn.classList.add('btn-disabled');
            inspectorBtn.classList.add('btn-disabled');
            injectBtn.disabled = true;
            inspectorBtn.disabled = true;
        }
        
        // 更新调试按钮状态
        const debugBtn = document.getElementById('debug-btn');
        const debugText = document.getElementById('debug-text');
        if (this.config.debugMode) {
            debugText.textContent = '关闭调试';
            debugBtn.classList.remove('btn-warning');
            debugBtn.classList.add('btn-danger');
        } else {
            debugText.textContent = '启用调试';
            debugBtn.classList.remove('btn-danger');
            debugBtn.classList.add('btn-warning');
        }
    }
    
    async updateStats() {
        try {
            // 获取缓存统计信息
            const storage = await chrome.storage.local.get(null);
            const cacheKeys = Object.keys(storage).filter(key => 
                key.startsWith('cache_') || key.startsWith('inspection_')
            );
            
            document.getElementById('cache-count').textContent = cacheKeys.length;
            
            // 计算缓存大小（粗略估算）
            const cacheSize = JSON.stringify(storage).length;
            const sizeInKB = Math.round(cacheSize / 1024);
            document.getElementById('cache-size').textContent = sizeInKB + 'KB';
            
        } catch (error) {
            document.getElementById('cache-count').textContent = '?';
            document.getElementById('cache-size').textContent = '?';
            this.log('统计信息更新失败', error);
        }
        
        // 更新最后更新时间
        document.getElementById('last-update').textContent = new Date().toLocaleTimeString();
    }
    
    setupEventListeners() {
        // 监听存储变化
        chrome.storage.onChanged.addListener((changes, namespace) => {
            if (namespace === 'sync' && changes.extensionConfig) {
                this.loadConfig().then(() => this.updateStatus());
            }
            if (namespace === 'local') {
                this.updateStats();
            }
        });
    }
    
    async injectScript() {
        if (!this.currentTab || !this.checkPageMatch()) {
            this.showAlert('当前页面不支持注入脚本', 'error');
            return;
        }
        
        try {
            const injectBtn = document.getElementById('inject-btn');
            const originalText = injectBtn.innerHTML;
            injectBtn.innerHTML = '<span class="loading"></span><span>注入中...</span>';
            injectBtn.disabled = true;
            
            // 发送注入消息给后台脚本
            const response = await chrome.runtime.sendMessage({
                action: 'injectScript',
                tabId: this.currentTab.id
            });
            
            if (response && response.success) {
                this.showAlert('脚本注入成功', 'success');
                await this.updateStatus();
            } else {
                this.showAlert('脚本注入失败: ' + (response?.error || '未知错误'), 'error');
            }
            
        } catch (error) {
            this.showAlert('脚本注入失败: ' + error.message, 'error');
            this.log('脚本注入失败', error);
        } finally {
            const injectBtn = document.getElementById('inject-btn');
            injectBtn.innerHTML = '<span>🚀</span><span>注入检核脚本</span>';
            injectBtn.disabled = false;
        }
    }
    
    async openInspector() {
        if (!this.currentTab || !this.checkPageMatch()) {
            this.showAlert('当前页面不支持图片检核功能', 'error');
            return;
        }
        
        try {
            // 发送消息给内容脚本打开检核界面
            await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'openInspector'
            });
            
            // 关闭弹出窗口
            window.close();
            
        } catch (error) {
            this.showAlert('无法打开图片检核: ' + error.message, 'error');
            this.log('打开检核界面失败', error);
        }
    }
    
    openOptions() {
        chrome.runtime.openOptionsPage();
        window.close();
    }
    
    async toggleDebug() {
        try {
            const newDebugMode = !this.config.debugMode;
            const updatedConfig = { ...this.config, debugMode: newDebugMode };
            
            await chrome.storage.sync.set({ extensionConfig: updatedConfig });
            this.config = updatedConfig;
            
            this.showAlert(
                newDebugMode ? '调试模式已启用' : '调试模式已关闭', 
                'info'
            );
            
            this.updateButtonStates(this.checkPageMatch());
            
        } catch (error) {
            this.showAlert('切换调试模式失败: ' + error.message, 'error');
            this.log('切换调试模式失败', error);
        }
    }
    
    async refreshCache() {
        try {
            // 发送刷新缓存消息给后台脚本
            const response = await chrome.runtime.sendMessage({
                action: 'refreshCache'
            });
            
            if (response && response.success) {
                this.showAlert('缓存已刷新', 'success');
                await this.updateStats();
            } else {
                this.showAlert('缓存刷新失败', 'error');
            }
            
        } catch (error) {
            this.showAlert('缓存刷新失败: ' + error.message, 'error');
            this.log('缓存刷新失败', error);
        }
    }
    
    async clearCache() {
        if (!confirm('确定要清除所有缓存吗？')) {
            return;
        }
        
        try {
            await chrome.storage.local.clear();
            this.showAlert('缓存已清除', 'success');
            await this.updateStats();
            
        } catch (error) {
            this.showAlert('缓存清除失败: ' + error.message, 'error');
            this.log('缓存清除失败', error);
        }
    }
    
    openHelp() {
        const helpUrl = 'https://github.com/your-repo/inspection-extension/wiki';
        chrome.tabs.create({ url: helpUrl });
        window.close();
    }
    
    reportIssue() {
        const issueUrl = 'https://github.com/your-repo/inspection-extension/issues';
        chrome.tabs.create({ url: issueUrl });
        window.close();
    }
    
    showAlert(message, type = 'info') {
        const container = document.getElementById('alert-container');
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.textContent = message;
        
        container.appendChild(alert);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 3000);
    }
    
    log(message, data = null) {
        if (this.config && this.config.debugMode) {
            const timestamp = new Date().toISOString();
            console.log(`[Popup ${timestamp}] ${message}`, data);
        }
    }
}

// 全局函数，供HTML调用
let popupManager;

function injectScript() {
    popupManager.injectScript();
}

function openInspector() {
    popupManager.openInspector();
}

function openOptions() {
    popupManager.openOptions();
}

function toggleDebug() {
    popupManager.toggleDebug();
}

function refreshCache() {
    popupManager.refreshCache();
}

function clearCache() {
    popupManager.clearCache();
}

function openHelp() {
    popupManager.openHelp();
}

function reportIssue() {
    popupManager.reportIssue();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    popupManager = new PopupManager();
});

// 定期更新状态
setInterval(() => {
    if (popupManager) {
        popupManager.updateStats();
    }
}, 5000);