# 图片检核管理Chrome扩展

一个功能强大的Chrome扩展，用于自动化图片检核管理，支持AI质检、缓存管理和状态更新。

## 🚀 主要功能

### 📋 核心功能
- **自动脚本注入**: 在指定页面自动注入检核脚本
- **图片检核管理**: 可视化管理图片检核状态
- **AI智能质检**: 集成AI接口进行图片质量检测
- **缓存管理**: 智能缓存数据，支持过期检查和手动清理
- **状态更新**: 实时更新图片检核状态到后台系统

### 🎯 高级功能
- **懒加载**: 图片懒加载优化性能
- **筛选功能**: 按检核状态、AI结果筛选图片
- **批量操作**: 支持批量AI检核
- **全屏模式**: 支持全屏查看检核界面
- **响应式设计**: 适配不同屏幕尺寸

## 📦 安装方法

### 开发者模式安装
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择本扩展的文件夹

### 配置设置
1. 点击扩展图标，选择"扩展设置"
2. 配置以下信息：
   - **目标页面**: 需要监听的页面域名
   - **接口配置**: 查询和更新接口地址
   - **AI配置**: AI接口URL和密钥
   - **缓存设置**: 缓存过期时间等

## 🔧 配置说明

### 页面监听配置
```
目标页面: assttest.dna-nev.com.cn, dms.dna-nev.com.cn
监听接口: /ly/mp/busicen/acc/accCheckInfo/queryPage.do
更新接口: /ly/mp/busicen/acc/accCheckRecordInfo/updateById.do
```

### AI配置示例
```
AI接口URL: https://api.openai.com/v1/chat/completions
AI模型: gpt-4-vision-preview
可信度阈值: 60
```

### 缓存配置
```
缓存过期时间: 30分钟
最大缓存大小: 100MB
自动清理: 启用
```

## 🎮 使用方法

### 基本使用流程
1. **访问目标页面**: 打开配置的目标页面
2. **注入脚本**: 扩展会自动注入或手动点击注入
3. **触发查询**: 点击页面查询按钮获取数据
4. **打开检核界面**: 点击"🖼️ 检核管理"按钮
5. **管理图片**: 查看、筛选、更新图片状态

### 快捷操作
- **Ctrl + 点击图片**: 快速放大查看
- **右键菜单**: 快速更新状态
- **键盘快捷键**: 
  - `F` - 全屏模式
  - `R` - 刷新数据
  - `Esc` - 关闭界面

## 🔍 功能详解

### 图片检核状态
- ✅ **合格**: 图片符合检核标准
- ❌ **不合格**: 图片不符合检核标准  
- ⏳ **未点检**: 尚未进行检核
- 🤖 **AI检核**: AI自动检核结果

### AI质检功能
- **自动检核**: 基于AI模型自动判断图片质量
- **可信度评分**: 0-100分的可信度评估
- **低可信度标记**: 可信度低于阈值的图片会特别标记
- **批量处理**: 支持批量AI检核

### 缓存机制
- **按活动分组**: 数据按活动/网点/图片三级结构缓存
- **过期检查**: 自动检查缓存是否过期
- **增量更新**: 只更新变化的数据
- **手动管理**: 支持手动清除和刷新缓存

## 🛠️ 开发说明

### 文件结构
```
chrome-extension/
├── manifest.json          # 扩展配置文件
├── background.js          # 后台服务脚本
├── content.js            # 内容脚本
├── injected-new.js       # 注入脚本
├── popup.html            # 弹窗页面
├── popup.js              # 弹窗脚本
├── options.html          # 设置页面
├── options.js            # 设置脚本
├── styles.css            # 样式文件
├── icons/                # 图标文件夹
└── README.md             # 说明文档
```

### 技术栈
- **前端**: HTML5, CSS3, JavaScript ES6+
- **Chrome API**: Storage, Tabs, Scripting, Runtime
- **AI集成**: OpenAI GPT-4 Vision API
- **缓存**: Chrome Storage API
- **UI框架**: 原生JavaScript + CSS Grid/Flexbox

## 🐛 故障排除

### 常见问题
1. **脚本注入失败**
   - 检查页面是否匹配配置的目标页面
   - 确认页面已完全加载
   - 查看控制台错误信息

2. **AI检核不工作**
   - 检查AI接口配置是否正确
   - 确认API密钥有效
   - 检查网络连接

3. **缓存问题**
   - 尝试清除缓存重新获取数据
   - 检查缓存过期时间设置
   - 查看存储空间是否充足

### 调试模式
1. 在设置中启用调试模式
2. 打开浏览器开发者工具
3. 查看Console标签页的日志信息
4. 检查Network标签页的网络请求

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 初始版本发布
- 🎯 基础图片检核功能
- 🤖 AI质检集成
- 💾 缓存管理系统
- 🎨 响应式UI设计

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📞 支持

如有问题或建议，请通过以下方式联系：
- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 📖 Wiki: [项目Wiki](https://github.com/your-repo/wiki)
