// 修复版注入脚本 - 解决 Illegal invocation 问题
(() => {
    'use strict';
    
    console.log('[Injected-Fixed] 修复版检核管理器开始加载');
    
    class InspectionManagerFixed {
        constructor() {
            // 基础配置
            this.API_PATH = "/ly/mp/busicen/acc/accCheckInfo/queryPage.do";
            this.UPDATE_PATH = "/ly/mp/busicen/acc/accCheckRecordInfo/updateById.do";
            this.SUCCESS_FIELD = "result";
            this.SUCCESS_VALUE = "1";
            
            // 页面路径检测
            this.TARGET_PATHS = [
                '/veModule/marketFee/marketBasicData/activityAuditManagement',
                '/veModule/marketFee/marketBasicData/recordViewing'
            ];
            
            // 从URL提取actId
            this.currentActId = this.extractActIdFromUrl();
            this.cacheKey = this.generateCacheKey();
            
            this.config = null;
            this.requestIntercepted = false;
            this.responseReceived = false;
            this.enableRequestMonitoring = false;

            this.init();
        }
        
        async init() {
            console.log('[Injected-Fixed] 检核管理器已初始化');
            console.log('[Injected-Fixed] 当前页面:', window.location.href);
            console.log('[Injected-Fixed] 当前actId:', this.currentActId);
            
            this.setupMessageListener();
            this.interceptNetworkRequests();
            
            // 只在目标页面创建按钮
            if (this.isTargetPage()) {
                console.log('[Injected-Fixed] 目标页面检测通过，创建操作按钮');
                this.createMainButton();
            } else {
                console.log('[Injected-Fixed] 非目标页面，跳过按钮创建');
            }
        }
        
        // 设置消息监听
        setupMessageListener() {
            window.addEventListener('message', (event) => {
                if (event.source !== window) return;
                
                if (event.data.type === 'EXTENSION_CONFIG') {
                    this.config = event.data.data;
                    console.log('[Injected-Fixed] 配置已接收:', this.config);
                }
            });
        }
        
        // 拦截网络请求 - 修复版
        interceptNetworkRequests() {
            console.log('[Injected-Fixed] 设置网络拦截');

            // 保存原始方法
            const originalFetch = window.fetch.bind(window);
            const originalXHROpen = XMLHttpRequest.prototype.open;
            const originalXHRSend = XMLHttpRequest.prototype.send;

            // 拦截fetch请求
            window.fetch = async (...args) => {
                const [url] = args;

                // 记录所有请求用于调试
                if (typeof url === 'string' && this.enableRequestMonitoring) {
                    console.log('[Fetch-Monitor] 请求URL:', url);
                }

                if (typeof url === 'string' && url.includes(this.API_PATH)) {
                    this.requestIntercepted = true;
                    console.log('[Fetch-Fixed] 捕获目标接口请求:', url);

                    try {
                        const response = await originalFetch(...args);
                        const clonedResponse = response.clone();

                        // 异步处理响应
                        clonedResponse.json().then(data => {
                            console.log('[Fetch-Fixed] 接口响应数据:', data);
                            this.responseReceived = true;
                            if (data[this.SUCCESS_FIELD] === this.SUCCESS_VALUE) {
                                console.log(`[Fetch-Fixed] 响应符合成功条件，保存缓存`);
                                this.saveCache(data);
                            } else {
                                console.log(`[Fetch-Fixed] 响应不符合成功条件`);
                            }
                        }).catch(e => {
                            console.log('[Fetch-Fixed] 解析响应失败（非JSON）:', e);
                        });

                        return response;
                    } catch (error) {
                        console.error('[Fetch-Fixed] 请求失败:', error);
                        return originalFetch(...args);
                    }
                }
                return originalFetch(...args);
            };
            
            // 拦截XMLHttpRequest
            XMLHttpRequest.prototype.open = function(method, url, ...args) {
                this._url = url;
                this._method = method;
                return originalXHROpen.call(this, method, url, ...args);
            };
            
            XMLHttpRequest.prototype.send = function(data) {
                if (this._url && this._url.includes(window.inspectionManagerFixed.API_PATH)) {
                    window.inspectionManagerFixed.requestIntercepted = true;
                    console.log('[XHR-Fixed] 捕获目标接口请求:', this._url);
                    
                    this.addEventListener("load", function() {
                        if (this.readyState === 4 && this.status === 200) {
                            window.inspectionManagerFixed.responseReceived = true;
                            try {
                                const responseData = JSON.parse(this.responseText);
                                console.log('[XHR-Fixed] 接口响应数据:', responseData);
                                if (responseData[window.inspectionManagerFixed.SUCCESS_FIELD] === window.inspectionManagerFixed.SUCCESS_VALUE) {
                                    console.log(`[XHR-Fixed] 响应符合成功条件，保存缓存`);
                                    window.inspectionManagerFixed.saveCache(responseData);
                                } else {
                                    console.log(`[XHR-Fixed] 响应不符合成功条件`);
                                }
                            } catch (e) {
                                console.log('[XHR-Fixed] 解析响应失败（非JSON）:', e);
                            }
                        }
                    });
                }
                return originalXHRSend.call(this, data);
            };
            
            console.log('[Injected-Fixed] 网络拦截设置完成');
        }
        
        // 从URL提取actId
        extractActIdFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const hashParams = new URLSearchParams(window.location.hash.split('?')[1] || '');
            return urlParams.get('actId') || hashParams.get('actId') || 'default';
        }
        
        // 生成缓存键
        generateCacheKey() {
            const actId = this.currentActId || this.extractActIdFromUrl();
            const date = new Date().toISOString().split('T')[0];
            return `inspection_${actId}_${date}`;
        }
        
        // 检查是否为目标页面
        isTargetPage() {
            const currentPath = window.location.hash;
            return this.TARGET_PATHS.some(path => currentPath.includes(path));
        }
        
        // 缓存工具
        saveCache(data) {
            try {
                localStorage.setItem(this.cacheKey, JSON.stringify({
                    data: data,
                    timestamp: Date.now()
                }));
                console.log('[Cache-Fixed] 数据已保存到缓存');
            } catch (e) {
                console.error('[Cache-Fixed] 保存缓存失败:', e);
            }
        }
        
        getCache() {
            try {
                const cached = localStorage.getItem(this.cacheKey);
                if (cached) {
                    const parsed = JSON.parse(cached);
                    const now = Date.now();
                    const expireTime = 30 * 60 * 1000; // 30分钟
                    
                    if (now - parsed.timestamp < expireTime) {
                        console.log('[Cache-Fixed] 使用缓存数据');
                        return parsed.data;
                    } else {
                        console.log('[Cache-Fixed] 缓存已过期');
                        localStorage.removeItem(this.cacheKey);
                    }
                }
                return null;
            } catch (e) {
                console.error('[Cache-Fixed] 读取缓存失败:', e);
                return null;
            }
        }
        
        clearCache() {
            try {
                localStorage.removeItem(this.cacheKey);
                console.log('[Cache-Fixed] 缓存已清除');
            } catch (e) {
                console.error('[Cache-Fixed] 清除缓存失败:', e);
            }
        }
        
        // 创建主操作按钮
        createMainButton() {
            // 检查按钮是否已存在
            if (document.getElementById('inspection-main-btn-fixed')) {
                return;
            }
            
            const button = document.createElement('button');
            button.id = 'inspection-main-btn-fixed';
            button.innerHTML = '🖼️ 检核管理';
            button.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                z-index: 9999;
                padding: 12px 18px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 600;
                box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                backdrop-filter: blur(10px);
                user-select: none;
            `;
            
            // 添加悬停效果
            button.addEventListener('mouseenter', () => {
                button.style.transform = 'translateY(-3px) scale(1.05)';
                button.style.boxShadow = '0 8px 30px rgba(102, 126, 234, 0.6)';
            });
            
            button.addEventListener('mouseleave', () => {
                button.style.transform = 'translateY(0) scale(1)';
                button.style.boxShadow = '0 4px 20px rgba(102, 126, 234, 0.4)';
            });
            
            button.addEventListener('click', () => {
                this.openInspectionModal();
            });
            
            document.body.appendChild(button);
            console.log('[Button-Fixed] 主操作按钮已创建');
        }
        
        // 打开检核弹窗
        async openInspectionModal() {
            console.log('[Modal-Fixed] 尝试打开检核界面');
            
            // 检查缓存
            const cachedData = this.getCache();
            if (cachedData) {
                this.showToast('使用缓存数据打开检核界面');
                this.createInspectionModal(cachedData);
            } else {
                this.showToast('无缓存数据，请先点击查询按钮获取数据');
                console.log('[Modal-Fixed] 无缓存数据，请先执行查询操作');
            }
        }
        
        // 创建图片检核界面
        createInspectionModal(data) {
            // 移除已存在的弹窗
            const existingModal = document.getElementById('inspection-modal-fixed');
            if (existingModal) {
                existingModal.remove();
            }

            const modal = document.createElement('div');
            modal.id = 'inspection-modal-fixed';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(0, 0, 0, 0.95);
                z-index: 999999;
                display: flex;
                flex-direction: column;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            `;

            // 创建标题栏
            const header = this.createModalHeader();
            modal.appendChild(header);

            // 创建工具栏
            const toolbar = this.createToolbar();
            modal.appendChild(toolbar);

            // 创建图片网格容器
            const content = document.createElement('div');
            content.style.cssText = `
                flex: 1;
                padding: 20px;
                overflow-y: auto;
                background: #f5f5f5;
            `;

            const imageGrid = this.createImageGrid(data);
            content.appendChild(imageGrid);
            modal.appendChild(content);

            // 绑定事件
            this.bindModalEvents(modal);

            document.body.appendChild(modal);
            console.log('[Modal-Fixed] 图片检核界面已打开');
        }

        // 创建模态框标题栏
        createModalHeader() {
            const header = document.createElement('div');
            header.style.cssText = `
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                display: flex;
                justify-content: space-between;
                align-items: center;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            `;

            const titleSection = document.createElement('div');
            titleSection.style.cssText = 'display: flex; align-items: center; gap: 15px;';

            const title = document.createElement('h2');
            title.textContent = `🖼️ 图片检核管理`;
            title.style.cssText = 'margin: 0; font-size: 24px; font-weight: 600;';

            const actInfo = document.createElement('span');
            actInfo.textContent = `ActId: ${this.currentActId}`;
            actInfo.style.cssText = 'background: rgba(255,255,255,0.2); padding: 4px 12px; border-radius: 20px; font-size: 12px;';

            titleSection.appendChild(title);
            titleSection.appendChild(actInfo);

            const closeBtn = document.createElement('button');
            closeBtn.id = 'close-modal-fixed';
            closeBtn.textContent = '✕ 关闭';
            closeBtn.style.cssText = `
                background: #f56c6c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                transition: background 0.3s;
            `;

            header.appendChild(titleSection);
            header.appendChild(closeBtn);

            return header;
        }

        // 创建工具栏
        createToolbar() {
            const toolbar = document.createElement('div');
            toolbar.style.cssText = `
                padding: 15px 20px;
                background: white;
                border-bottom: 1px solid #e0e0e0;
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                gap: 15px;
            `;

            // 左侧筛选区域
            const filterSection = document.createElement('div');
            filterSection.style.cssText = 'display: flex; align-items: center; gap: 15px; flex-wrap: wrap;';

            const filterLabel = document.createElement('span');
            filterLabel.textContent = '筛选：';
            filterLabel.style.cssText = 'font-weight: 600; color: #333;';

            // 状态筛选
            const statusFilter = document.createElement('select');
            statusFilter.id = 'status-filter';
            statusFilter.style.cssText = `
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 6px;
                background: white;
                cursor: pointer;
            `;
            statusFilter.innerHTML = `
                <option value="all">全部状态</option>
                <option value="1">✅ 合格</option>
                <option value="2">❌ 不合格</option>
                <option value="0">⏳ 未点检</option>
            `;

            filterSection.appendChild(filterLabel);
            filterSection.appendChild(statusFilter);

            // 右侧操作区域
            const actionSection = document.createElement('div');
            actionSection.style.cssText = 'display: flex; align-items: center; gap: 10px;';

            // 批量AI检核按钮
            const batchAIBtn = document.createElement('button');
            batchAIBtn.textContent = '🤖 批量AI检核';
            batchAIBtn.style.cssText = `
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                transition: transform 0.2s;
            `;

            // 刷新数据按钮
            const refreshBtn = document.createElement('button');
            refreshBtn.textContent = '🔄 刷新数据';
            refreshBtn.style.cssText = `
                background: #28a745;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                transition: transform 0.2s;
            `;

            actionSection.appendChild(batchAIBtn);
            actionSection.appendChild(refreshBtn);

            toolbar.appendChild(filterSection);
            toolbar.appendChild(actionSection);

            return toolbar;
        }

        // 创建图片网格
        createImageGrid(data) {
            const gridContainer = document.createElement('div');
            gridContainer.id = 'image-grid-container';
            gridContainer.style.cssText = `
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                gap: 20px;
                padding: 20px 0;
            `;

            // 解析数据并创建图片卡片
            if (data && data.rows) {
                data.rows.forEach(row => {
                    if (row.items) {
                        row.items.forEach(item => {
                            if (item.rmarkLst) {
                                item.rmarkLst.forEach(rmark => {
                                    const imageCard = this.createImageCard(rmark, item.checkItemTitle, row.actName);
                                    gridContainer.appendChild(imageCard);
                                });
                            }
                        });
                    }
                });
            }

            // 如果没有数据，显示空状态
            if (gridContainer.children.length === 0) {
                const emptyState = document.createElement('div');
                emptyState.style.cssText = `
                    grid-column: 1 / -1;
                    text-align: center;
                    padding: 60px 20px;
                    color: #999;
                    font-size: 16px;
                `;
                emptyState.innerHTML = `
                    <div style="font-size: 48px; margin-bottom: 20px;">📷</div>
                    <div>暂无图片数据</div>
                    <div style="font-size: 14px; margin-top: 10px;">请先在页面上执行查询操作</div>
                `;
                gridContainer.appendChild(emptyState);
            }

            return gridContainer;
        }

        // 绑定模态框事件
        bindModalEvents(modal) {
            // 关闭按钮事件
            modal.querySelector('#close-modal-fixed').addEventListener('click', () => {
                modal.remove();
            });

            // 点击遮罩关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }

        // 创建单个图片卡片
        createImageCard(rmark, checkItemTitle, actName) {
            const card = document.createElement('div');
            card.className = 'image-card';
            card.dataset.markId = rmark.markId;
            card.dataset.markFlag = rmark.markFlag || '0';
            card.style.cssText = `
                background: white;
                border-radius: 12px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                overflow: hidden;
                transition: all 0.3s ease;
                cursor: pointer;
                position: relative;
            `;

            // 悬停效果
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-4px)';
                card.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
            });
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
                card.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
            });

            // 状态标识
            const statusBadge = document.createElement('div');
            statusBadge.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                z-index: 10;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 600;
                color: white;
                backdrop-filter: blur(10px);
            `;

            const flagValue = rmark.markFlag || '0';
            switch (flagValue) {
                case '1':
                    statusBadge.textContent = '✅ 合格';
                    statusBadge.style.background = 'rgba(40, 167, 69, 0.9)';
                    break;
                case '2':
                    statusBadge.textContent = '❌ 不合格';
                    statusBadge.style.background = 'rgba(220, 53, 69, 0.9)';
                    break;
                default:
                    statusBadge.textContent = '⏳ 未点检';
                    statusBadge.style.background = 'rgba(108, 117, 125, 0.9)';
            }

            // 图片容器
            const imageContainer = document.createElement('div');
            imageContainer.style.cssText = `
                width: 100%;
                height: 200px;
                background: #f8f9fa;
                position: relative;
                overflow: hidden;
            `;

            // 图片元素
            const img = document.createElement('img');
            img.src = rmark.checkItemValue;
            img.alt = checkItemTitle;
            img.style.cssText = `
                width: 100%;
                height: 100%;
                object-fit: cover;
                transition: transform 0.3s ease;
            `;

            // 图片加载错误处理
            img.onerror = () => {
                imageContainer.innerHTML = `
                    <div style="
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 100%;
                        color: #999;
                        flex-direction: column;
                        gap: 10px;
                    ">
                        <div style="font-size: 24px;">🖼️</div>
                        <div style="font-size: 14px;">图片加载失败</div>
                    </div>
                `;
            };

            imageContainer.appendChild(img);
            imageContainer.appendChild(statusBadge);

            // 信息区域
            const infoSection = document.createElement('div');
            infoSection.style.cssText = 'padding: 15px;';

            // 标题
            const title = document.createElement('h4');
            title.textContent = checkItemTitle;
            title.style.cssText = `
                margin: 0 0 8px 0;
                font-size: 16px;
                font-weight: 600;
                color: #333;
                line-height: 1.4;
            `;

            // 详细信息
            const details = document.createElement('div');
            details.style.cssText = 'font-size: 12px; color: #666; margin-bottom: 12px;';
            details.innerHTML = `
                <div>ID: ${rmark.markId}</div>
                ${rmark.markReason ? `<div style="margin-top: 4px;">原因: ${rmark.markReason}</div>` : ''}
            `;

            // 操作按钮区域
            const actionButtons = this.createImageCardActions(rmark);

            infoSection.appendChild(title);
            infoSection.appendChild(details);
            infoSection.appendChild(actionButtons);

            card.appendChild(imageContainer);
            card.appendChild(infoSection);

            return card;
        }

        // 创建图片卡片操作按钮
        createImageCardActions(rmark) {
            const actionContainer = document.createElement('div');
            actionContainer.style.cssText = `
                display: flex;
                gap: 8px;
                flex-wrap: wrap;
            `;

            // 状态切换按钮
            const statusButtons = ['1', '2', '0'].map(status => {
                const btn = document.createElement('button');
                const isActive = (rmark.markFlag || '0') === status;

                switch (status) {
                    case '1':
                        btn.textContent = '✅ 合格';
                        btn.style.background = isActive ? '#28a745' : '#e9ecef';
                        btn.style.color = isActive ? 'white' : '#6c757d';
                        break;
                    case '2':
                        btn.textContent = '❌ 不合格';
                        btn.style.background = isActive ? '#dc3545' : '#e9ecef';
                        btn.style.color = isActive ? 'white' : '#6c757d';
                        break;
                    case '0':
                        btn.textContent = '⏳ 未点检';
                        btn.style.background = isActive ? '#6c757d' : '#e9ecef';
                        btn.style.color = isActive ? 'white' : '#6c757d';
                        break;
                }

                btn.style.cssText += `
                    border: none;
                    padding: 6px 12px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 12px;
                    font-weight: 500;
                    transition: all 0.2s;
                    flex: 1;
                    min-width: 70px;
                `;

                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.updateImageStatus(rmark.markId, status);
                });

                return btn;
            });

            statusButtons.forEach(btn => actionContainer.appendChild(btn));

            // AI检核按钮
            const aiBtn = document.createElement('button');
            aiBtn.textContent = '🤖 AI检核';
            aiBtn.style.cssText = `
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                font-weight: 500;
                transition: all 0.2s;
                width: 100%;
                margin-top: 8px;
            `;

            aiBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.performAICheck(rmark);
            });

            actionContainer.appendChild(aiBtn);

            return actionContainer;
        }

        // 更新图片状态
        updateImageStatus(markId, newStatus) {
            console.log(`[Status] 更新图片状态: ${markId} -> ${newStatus}`);
            this.showToast(`图片状态已更新为: ${newStatus === '1' ? '合格' : newStatus === '2' ? '不合格' : '未点检'}`, 'success');

            // 这里可以添加实际的状态更新逻辑
            // 比如发送请求到后台API
        }

        // 执行AI检核
        performAICheck(rmark) {
            console.log(`[AI] 开始AI检核: ${rmark.markId}`);
            this.showToast('正在进行AI检核...', 'info');

            // 模拟AI检核过程
            setTimeout(() => {
                const confidence = Math.floor(Math.random() * 40) + 60; // 60-100的随机可信度
                const result = confidence >= 80 ? '高可信度' : confidence >= 60 ? '中可信度' : '低可信度';
                this.showToast(`AI检核完成: ${result} (${confidence}%)`, 'success');
            }, 2000);
        }

        // 显示Toast消息
        showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000000;
                background: ${type === 'error' ? '#f56c6c' : type === 'success' ? '#67c23a' : '#409eff'};
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            toast.textContent = message;
            
            document.body.appendChild(toast);
            
            // 显示动画
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 10);
            
            // 自动隐藏
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }
    }
    
    // 创建全局实例
    window.inspectionManagerFixed = new InspectionManagerFixed();
    
    // 调试工具
    window.debugFixed = {
        checkStatus: () => {
            const manager = window.inspectionManagerFixed;
            console.log('🔧 [Debug-Fixed] 扩展状态:');
            console.log('  - 请求拦截:', manager.requestIntercepted);
            console.log('  - 响应接收:', manager.responseReceived);
            console.log('  - 当前actId:', manager.currentActId);
            console.log('  - 缓存键:', manager.cacheKey);
        },
        
        simulateData: () => {
            const mockData = {
                result: "1",
                rows: [
                    {
                        actId: window.inspectionManagerFixed.currentActId,
                        actName: "基础外拓活动",
                        items: [
                            {
                                checkItemTitle: "外观检查",
                                rmarkLst: [
                                    {
                                        markId: "mark_001",
                                        markFlag: "1",
                                        markReason: "",
                                        checkItemValue: "https://picsum.photos/400/300?random=1"
                                    },
                                    {
                                        markId: "mark_002",
                                        markFlag: "2",
                                        markReason: "图片模糊",
                                        checkItemValue: "https://picsum.photos/400/300?random=2"
                                    },
                                    {
                                        markId: "mark_003",
                                        markFlag: "0",
                                        markReason: "",
                                        checkItemValue: "https://picsum.photos/400/300?random=3"
                                    }
                                ]
                            },
                            {
                                checkItemTitle: "环境检查",
                                rmarkLst: [
                                    {
                                        markId: "mark_004",
                                        markFlag: "1",
                                        markReason: "",
                                        checkItemValue: "https://picsum.photos/400/300?random=4"
                                    },
                                    {
                                        markId: "mark_005",
                                        markFlag: "0",
                                        markReason: "",
                                        checkItemValue: "https://picsum.photos/400/300?random=5"
                                    }
                                ]
                            },
                            {
                                checkItemTitle: "人员检查",
                                rmarkLst: [
                                    {
                                        markId: "mark_006",
                                        markFlag: "2",
                                        markReason: "人员不规范",
                                        checkItemValue: "https://picsum.photos/400/300?random=6"
                                    },
                                    {
                                        markId: "mark_007",
                                        markFlag: "1",
                                        markReason: "",
                                        checkItemValue: "https://picsum.photos/400/300?random=7"
                                    },
                                    {
                                        markId: "mark_008",
                                        markFlag: "0",
                                        markReason: "",
                                        checkItemValue: "https://picsum.photos/400/300?random=8"
                                    }
                                ]
                            }
                        ]
                    }
                ]
            };
            window.inspectionManagerFixed.saveCache(mockData);
            console.log('🔧 [Debug-Fixed] 模拟数据已保存 (8张图片)');
        },
        
        openModal: () => {
            window.inspectionManagerFixed.openInspectionModal();
        },
        
        clearCache: () => {
            window.inspectionManagerFixed.clearCache();
        },

        // 监控网络请求
        monitorRequests: () => {
            console.log('🔧 [Debug-Fixed] 开始监控网络请求...');
            window.inspectionManagerFixed.enableRequestMonitoring = true;
        },

        // 停止监控网络请求
        stopMonitoring: () => {
            console.log('🔧 [Debug-Fixed] 停止监控网络请求');
            window.inspectionManagerFixed.enableRequestMonitoring = false;
        },

        // 查看当前API路径配置
        showConfig: () => {
            const manager = window.inspectionManagerFixed;
            console.log('🔧 [Debug-Fixed] 当前配置:');
            console.log('  - API路径:', manager.API_PATH);
            console.log('  - 更新路径:', manager.UPDATE_PATH);
            console.log('  - 目标页面路径:', manager.TARGET_PATHS);
            console.log('  - 当前页面匹配:', manager.isTargetPage());
        }
    };
    
    console.log('[Injected-Fixed] 修复版检核管理器已初始化');
    console.log('[Injected-Fixed] 使用 debugFixed.checkStatus() 检查状态');
    
})();
