// 修复版注入脚本 - 解决 Illegal invocation 问题
(() => {
    'use strict';
    
    console.log('[Injected-Fixed] 修复版检核管理器开始加载');
    
    class InspectionManagerFixed {
        constructor() {
            // 基础配置
            this.API_PATH = "/ly/mp/busicen/acc/accCheckInfo/queryPage.do";
            this.UPDATE_PATH = "/ly/mp/busicen/acc/accCheckRecordInfo/updateById.do";
            this.SUCCESS_FIELD = "result";
            this.SUCCESS_VALUE = "1";
            
            // 页面路径检测
            this.TARGET_PATHS = [
                '/veModule/marketFee/marketBasicData/activityAuditManagement',
                '/veModule/marketFee/marketBasicData/recordViewing'
            ];
            
            // 从URL提取actId
            this.currentActId = this.extractActIdFromUrl();
            this.cacheKey = this.generateCacheKey();
            
            this.config = null;
            this.requestIntercepted = false;
            this.responseReceived = false;
            this.enableRequestMonitoring = false;

            this.init();
        }
        
        async init() {
            console.log('[Injected-Fixed] 检核管理器已初始化');
            console.log('[Injected-Fixed] 当前页面:', window.location.href);
            console.log('[Injected-Fixed] 当前actId:', this.currentActId);
            
            this.setupMessageListener();
            this.interceptNetworkRequests();
            
            // 只在目标页面创建按钮
            if (this.isTargetPage()) {
                console.log('[Injected-Fixed] 目标页面检测通过，创建操作按钮');
                this.createMainButton();
            } else {
                console.log('[Injected-Fixed] 非目标页面，跳过按钮创建');
            }
        }
        
        // 设置消息监听
        setupMessageListener() {
            window.addEventListener('message', (event) => {
                if (event.source !== window) return;
                
                if (event.data.type === 'EXTENSION_CONFIG') {
                    this.config = event.data.data;
                    console.log('[Injected-Fixed] 配置已接收:', this.config);
                }
            });
        }
        
        // 拦截网络请求 - 修复版
        interceptNetworkRequests() {
            console.log('[Injected-Fixed] 设置网络拦截');

            // 保存原始方法
            const originalFetch = window.fetch.bind(window);
            const originalXHROpen = XMLHttpRequest.prototype.open;
            const originalXHRSend = XMLHttpRequest.prototype.send;

            // 拦截fetch请求
            window.fetch = async (...args) => {
                const [url] = args;

                // 记录所有请求用于调试
                if (typeof url === 'string' && this.enableRequestMonitoring) {
                    console.log('[Fetch-Monitor] 请求URL:', url);
                }

                if (typeof url === 'string' && url.includes(this.API_PATH)) {
                    this.requestIntercepted = true;
                    console.log('[Fetch-Fixed] 捕获目标接口请求:', url);

                    try {
                        const response = await originalFetch(...args);
                        const clonedResponse = response.clone();

                        // 异步处理响应
                        clonedResponse.json().then(data => {
                            console.log('[Fetch-Fixed] 接口响应数据:', data);
                            this.responseReceived = true;
                            if (data[this.SUCCESS_FIELD] === this.SUCCESS_VALUE) {
                                console.log(`[Fetch-Fixed] 响应符合成功条件，保存缓存`);
                                this.saveCache(data);
                            } else {
                                console.log(`[Fetch-Fixed] 响应不符合成功条件`);
                            }
                        }).catch(e => {
                            console.log('[Fetch-Fixed] 解析响应失败（非JSON）:', e);
                        });

                        return response;
                    } catch (error) {
                        console.error('[Fetch-Fixed] 请求失败:', error);
                        return originalFetch(...args);
                    }
                }
                return originalFetch(...args);
            };
            
            // 拦截XMLHttpRequest
            XMLHttpRequest.prototype.open = function(method, url, ...args) {
                this._url = url;
                this._method = method;
                return originalXHROpen.call(this, method, url, ...args);
            };
            
            XMLHttpRequest.prototype.send = function(data) {
                if (this._url && this._url.includes(window.inspectionManagerFixed.API_PATH)) {
                    window.inspectionManagerFixed.requestIntercepted = true;
                    console.log('[XHR-Fixed] 捕获目标接口请求:', this._url);
                    
                    this.addEventListener("load", function() {
                        if (this.readyState === 4 && this.status === 200) {
                            window.inspectionManagerFixed.responseReceived = true;
                            try {
                                const responseData = JSON.parse(this.responseText);
                                console.log('[XHR-Fixed] 接口响应数据:', responseData);
                                if (responseData[window.inspectionManagerFixed.SUCCESS_FIELD] === window.inspectionManagerFixed.SUCCESS_VALUE) {
                                    console.log(`[XHR-Fixed] 响应符合成功条件，保存缓存`);
                                    window.inspectionManagerFixed.saveCache(responseData);
                                } else {
                                    console.log(`[XHR-Fixed] 响应不符合成功条件`);
                                }
                            } catch (e) {
                                console.log('[XHR-Fixed] 解析响应失败（非JSON）:', e);
                            }
                        }
                    });
                }
                return originalXHRSend.call(this, data);
            };
            
            console.log('[Injected-Fixed] 网络拦截设置完成');
        }
        
        // 从URL提取actId
        extractActIdFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const hashParams = new URLSearchParams(window.location.hash.split('?')[1] || '');
            return urlParams.get('actId') || hashParams.get('actId') || 'default';
        }
        
        // 生成缓存键
        generateCacheKey() {
            const actId = this.currentActId || this.extractActIdFromUrl();
            const date = new Date().toISOString().split('T')[0];
            return `inspection_${actId}_${date}`;
        }
        
        // 检查是否为目标页面
        isTargetPage() {
            const currentPath = window.location.hash;
            return this.TARGET_PATHS.some(path => currentPath.includes(path));
        }
        
        // 缓存工具
        saveCache(data) {
            try {
                localStorage.setItem(this.cacheKey, JSON.stringify({
                    data: data,
                    timestamp: Date.now()
                }));
                console.log('[Cache-Fixed] 数据已保存到缓存');
            } catch (e) {
                console.error('[Cache-Fixed] 保存缓存失败:', e);
            }
        }
        
        getCache() {
            try {
                const cached = localStorage.getItem(this.cacheKey);
                if (cached) {
                    const parsed = JSON.parse(cached);
                    const now = Date.now();
                    const expireTime = 30 * 60 * 1000; // 30分钟
                    
                    if (now - parsed.timestamp < expireTime) {
                        console.log('[Cache-Fixed] 使用缓存数据');
                        return parsed.data;
                    } else {
                        console.log('[Cache-Fixed] 缓存已过期');
                        localStorage.removeItem(this.cacheKey);
                    }
                }
                return null;
            } catch (e) {
                console.error('[Cache-Fixed] 读取缓存失败:', e);
                return null;
            }
        }
        
        clearCache() {
            try {
                localStorage.removeItem(this.cacheKey);
                console.log('[Cache-Fixed] 缓存已清除');
            } catch (e) {
                console.error('[Cache-Fixed] 清除缓存失败:', e);
            }
        }
        
        // 创建主操作按钮
        createMainButton() {
            // 检查按钮是否已存在
            if (document.getElementById('inspection-main-btn-fixed')) {
                return;
            }
            
            const button = document.createElement('button');
            button.id = 'inspection-main-btn-fixed';
            button.innerHTML = '🖼️ 检核管理';
            button.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                z-index: 9999;
                padding: 12px 18px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 600;
                box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                backdrop-filter: blur(10px);
                user-select: none;
            `;
            
            // 添加悬停效果
            button.addEventListener('mouseenter', () => {
                button.style.transform = 'translateY(-3px) scale(1.05)';
                button.style.boxShadow = '0 8px 30px rgba(102, 126, 234, 0.6)';
            });
            
            button.addEventListener('mouseleave', () => {
                button.style.transform = 'translateY(0) scale(1)';
                button.style.boxShadow = '0 4px 20px rgba(102, 126, 234, 0.4)';
            });
            
            button.addEventListener('click', () => {
                this.openInspectionModal();
            });
            
            document.body.appendChild(button);
            console.log('[Button-Fixed] 主操作按钮已创建');
        }
        
        // 打开检核弹窗
        async openInspectionModal() {
            console.log('[Modal-Fixed] 尝试打开检核界面');
            
            // 检查缓存
            const cachedData = this.getCache();
            if (cachedData) {
                this.showToast('使用缓存数据打开检核界面');
                this.createInspectionModal(cachedData);
            } else {
                this.showToast('无缓存数据，请先点击查询按钮获取数据');
                console.log('[Modal-Fixed] 无缓存数据，请先执行查询操作');
            }
        }
        
        // 创建检核弹窗（简化版）
        createInspectionModal(data) {
            // 移除已存在的弹窗
            const existingModal = document.getElementById('inspection-modal-fixed');
            if (existingModal) {
                existingModal.remove();
            }
            
            const modal = document.createElement('div');
            modal.id = 'inspection-modal-fixed';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(0, 0, 0, 0.9);
                z-index: 999999;
                display: flex;
                flex-direction: column;
                color: white;
            `;
            
            // 创建标题栏
            const header = document.createElement('div');
            header.style.cssText = 'padding: 20px; background: #333; display: flex; justify-content: space-between; align-items: center;';

            const title = document.createElement('h2');
            title.textContent = `🖼️ 图片检核管理 (ActId: ${this.currentActId})`;
            title.style.cssText = 'margin: 0; color: white;';

            const closeBtn = document.createElement('button');
            closeBtn.id = 'close-modal-fixed';
            closeBtn.textContent = '关闭';
            closeBtn.style.cssText = 'background: #f56c6c; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer;';

            header.appendChild(title);
            header.appendChild(closeBtn);

            // 创建内容区域
            const content = document.createElement('div');
            content.style.cssText = 'flex: 1; padding: 20px; overflow-y: auto;';

            const pre = document.createElement('pre');
            pre.style.cssText = 'background: #222; padding: 20px; border-radius: 8px; overflow-x: auto; white-space: pre-wrap; color: white; margin: 0;';
            pre.textContent = JSON.stringify(data, null, 2);

            content.appendChild(pre);
            modal.appendChild(header);
            modal.appendChild(content);
            
            // 绑定关闭事件
            modal.querySelector('#close-modal-fixed').addEventListener('click', () => {
                modal.remove();
            });
            
            // 点击遮罩关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            });
            
            document.body.appendChild(modal);
            console.log('[Modal-Fixed] 检核界面已打开');
        }
        
        // 显示Toast消息
        showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000000;
                background: ${type === 'error' ? '#f56c6c' : type === 'success' ? '#67c23a' : '#409eff'};
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            toast.textContent = message;
            
            document.body.appendChild(toast);
            
            // 显示动画
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 10);
            
            // 自动隐藏
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }
    }
    
    // 创建全局实例
    window.inspectionManagerFixed = new InspectionManagerFixed();
    
    // 调试工具
    window.debugFixed = {
        checkStatus: () => {
            const manager = window.inspectionManagerFixed;
            console.log('🔧 [Debug-Fixed] 扩展状态:');
            console.log('  - 请求拦截:', manager.requestIntercepted);
            console.log('  - 响应接收:', manager.responseReceived);
            console.log('  - 当前actId:', manager.currentActId);
            console.log('  - 缓存键:', manager.cacheKey);
        },
        
        simulateData: () => {
            const mockData = {
                result: "1",
                rows: [
                    {
                        actId: window.inspectionManagerFixed.currentActId,
                        actName: "测试活动",
                        items: [
                            {
                                checkItemTitle: "外观检查",
                                rmarkLst: [
                                    {
                                        markId: "mark_001",
                                        markFlag: "1",
                                        markReason: "",
                                        checkItemValue: "https://picsum.photos/400/300?random=1"
                                    }
                                ]
                            }
                        ]
                    }
                ]
            };
            window.inspectionManagerFixed.saveCache(mockData);
            console.log('🔧 [Debug-Fixed] 模拟数据已保存');
        },
        
        openModal: () => {
            window.inspectionManagerFixed.openInspectionModal();
        },
        
        clearCache: () => {
            window.inspectionManagerFixed.clearCache();
        },

        // 监控网络请求
        monitorRequests: () => {
            console.log('🔧 [Debug-Fixed] 开始监控网络请求...');
            window.inspectionManagerFixed.enableRequestMonitoring = true;
        },

        // 停止监控网络请求
        stopMonitoring: () => {
            console.log('🔧 [Debug-Fixed] 停止监控网络请求');
            window.inspectionManagerFixed.enableRequestMonitoring = false;
        },

        // 查看当前API路径配置
        showConfig: () => {
            const manager = window.inspectionManagerFixed;
            console.log('🔧 [Debug-Fixed] 当前配置:');
            console.log('  - API路径:', manager.API_PATH);
            console.log('  - 更新路径:', manager.UPDATE_PATH);
            console.log('  - 目标页面路径:', manager.TARGET_PATHS);
            console.log('  - 当前页面匹配:', manager.isTargetPage());
        }
    };
    
    console.log('[Injected-Fixed] 修复版检核管理器已初始化');
    console.log('[Injected-Fixed] 使用 debugFixed.checkStatus() 检查状态');
    
})();
