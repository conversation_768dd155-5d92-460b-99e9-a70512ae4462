# 图片检核管理Chrome扩展 - 安装指南

## 📦 快速安装

### 方法一：开发者模式安装（推荐）

1. **打开Chrome扩展管理页面**
   - 在Chrome地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

2. **启用开发者模式**
   - 点击右上角的"开发者模式"开关
   - 确保开关处于开启状态

3. **加载扩展**
   - 点击"加载已解压的扩展程序"按钮
   - 选择本扩展的文件夹（chrome-extension）
   - 点击"选择文件夹"

4. **验证安装**
   - 扩展列表中应该出现"图片检核管理扩展"
   - 浏览器工具栏会显示扩展图标 🖼️

## ⚙️ 初始配置

### 1. 打开配置页面
- 点击扩展图标
- 选择"扩展设置"
- 或者在扩展管理页面点击"选项"

### 2. 基础配置

#### 页面监听设置
```
目标页面域名：
assttest.dna-nev.com.cn
dms.dna-nev.com.cn

监听接口路径：
/ly/mp/busicen/acc/accCheckInfo/queryPage.do

更新接口路径：
/ly/mp/busicen/acc/accCheckRecordInfo/updateById.do
```

#### 按钮选择器配置
```
.u-btn-right .right-btn button.el-button--primary
button:contains("查询")
button[type="button"].el-button--primary
#queryBtn
.search-btn
```

### 3. AI配置（可选）

如果需要使用AI质检功能：

```
AI接口URL: https://api.openai.com/v1/chat/completions
AI模型: gpt-4-vision-preview
API密钥: 您的OpenAI API密钥
可信度阈值: 60
```

### 4. 缓存设置

```
缓存过期时间: 30分钟
最大缓存大小: 100MB
自动清理缓存: 启用
```

## 🧪 功能测试

### 1. 基础功能测试

1. **访问测试页面**
   - 打开 `chrome-extension/test.html`
   - 检查扩展状态指示器

2. **脚本注入测试**
   - 访问配置的目标页面
   - 点击扩展图标查看注入状态
   - 或手动点击"注入检核脚本"

3. **数据拦截测试**
   - 在目标页面点击查询按钮
   - 检查是否成功拦截API响应
   - 查看浏览器控制台日志

### 2. 检核界面测试

1. **打开检核界面**
   - 点击页面上的"🖼️ 检核管理"按钮
   - 或通过扩展弹窗打开

2. **功能验证**
   - 图片懒加载是否正常
   - 筛选功能是否工作
   - 状态更新是否成功

### 3. AI功能测试（需要API密钥）

1. **单张图片AI检核**
   - 点击图片卡片上的"🤖 AI检核"按钮
   - 查看返回的可信度分数

2. **批量AI检核**
   - 点击工具栏的"🤖 批量AI检核"按钮
   - 观察批量处理进度

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 扩展无法加载
**症状**: 扩展列表中没有出现或显示错误
**解决方案**:
- 检查文件夹路径是否正确
- 确认manifest.json文件格式正确
- 查看扩展管理页面的错误信息

#### 2. 脚本注入失败
**症状**: 页面上没有出现检核管理按钮
**解决方案**:
- 检查当前页面是否匹配配置的目标页面
- 确认页面已完全加载
- 手动点击"注入检核脚本"按钮
- 查看浏览器控制台错误信息

#### 3. API拦截不工作
**症状**: 点击查询按钮后没有数据
**解决方案**:
- 检查接口路径配置是否正确
- 确认页面使用的是配置的接口地址
- 查看Network标签页的网络请求
- 检查响应数据格式是否符合预期

#### 4. AI功能无响应
**症状**: AI检核按钮点击后无反应
**解决方案**:
- 检查AI接口URL和API密钥配置
- 确认网络连接正常
- 查看控制台错误信息
- 检查API配额是否用完

#### 5. 缓存问题
**症状**: 数据不更新或显示旧数据
**解决方案**:
- 点击"清除缓存"按钮
- 检查缓存过期时间设置
- 手动刷新数据
- 重启浏览器

### 调试模式

1. **启用调试模式**
   - 在扩展设置中启用调试模式
   - 或在扩展弹窗中点击"启用调试"

2. **查看日志**
   - 打开浏览器开发者工具 (F12)
   - 切换到Console标签页
   - 查看以[Injected]、[Content]、[Background]开头的日志

3. **网络监控**
   - 在开发者工具中切换到Network标签页
   - 重新执行操作
   - 查看网络请求和响应

## 📋 检查清单

安装完成后，请确认以下项目：

- [ ] 扩展已成功加载到Chrome
- [ ] 扩展图标显示在工具栏
- [ ] 基础配置已完成
- [ ] 目标页面能够正常注入脚本
- [ ] API拦截功能正常工作
- [ ] 检核界面能够正常打开
- [ ] 图片加载和显示正常
- [ ] 状态更新功能正常
- [ ] 缓存功能正常工作
- [ ] AI功能配置正确（如果使用）

## 🆘 获取帮助

如果遇到问题，可以通过以下方式获取帮助：

1. **查看文档**
   - 阅读 README.md 了解详细功能
   - 查看代码注释了解实现细节

2. **调试信息**
   - 启用调试模式查看详细日志
   - 使用测试页面验证功能

3. **联系支持**
   - 📧 Email: <EMAIL>
   - 🐛 Issues: GitHub Issues
   - 📖 Wiki: 项目Wiki

## 🔄 更新扩展

当有新版本时：

1. **备份配置**
   - 在设置页面导出当前配置

2. **更新文件**
   - 替换扩展文件夹中的文件
   - 保持配置文件不变

3. **重新加载**
   - 在扩展管理页面点击"重新加载"
   - 或重启浏览器

4. **验证功能**
   - 按照测试清单重新验证功能
   - 导入之前备份的配置

---

**注意**: 本扩展仅供内部使用，请勿在生产环境中使用未经充分测试的版本。
