(() => {
    // 基础配置
    const API_PATH = "/queryPage.do";
    const SUCCESS_FIELD = "result";
    const SUCCESS_VALUE = "1";
    const cacheKey = `inspection_data_${new Date().toISOString().split('T')[0]}`;

    // 缓存工具
    const Cache = {
        save: (data) => {
            localStorage.setItem(cacheKey, JSON.stringify({ data, time: new Date().toLocaleString() }));
            console.log("[缓存] 已保存数据，key:", cacheKey);
        },
        get: () => {
            const raw = localStorage.getItem(cacheKey);
            return raw ? JSON.parse(raw).data : null;
        },
        clear: () => localStorage.removeItem(cacheKey)
    };

    // 请求拦截
    let requestIntercepted = false;
    let responseReceived = false;

    // 拦截fetch
    const originFetch = window.fetch;
    window.fetch = async (...args) => {
        const [url] = args;
        if (url.includes(API_PATH)) {
            requestIntercepted = true;
            console.log("[Fetch] 捕获目标接口请求:", url);
            const res = await originFetch.apply(this, args);
            const clone = res.clone();
            try {
                const data = await clone.json();
                console.log("[Fetch] 接口响应数据:", data);
                responseReceived = true;
                if (data[SUCCESS_FIELD] === SUCCESS_VALUE) {
                    console.log(`[Fetch] 响应符合成功条件（${SUCCESS_FIELD}=${SUCCESS_VALUE}），保存缓存`);
                    Cache.save(data);
                } else {
                    console.warn(`[Fetch] 响应不符合成功条件！${SUCCESS_FIELD}=${data[SUCCESS_FIELD]}`);
                }
            } catch (e) {
                console.error("[Fetch] 解析响应失败（非JSON）:", e);
            }
            return res;
        }
        return originFetch.apply(this, args);
    };

    // 拦截XMLHttpRequest
    const originXhrOpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function (method, url) {
        if (url.includes(API_PATH)) {
            requestIntercepted = true;
            console.log("[XHR] 捕获目标接口请求:", url);
            this.addEventListener("load", () => {
                responseReceived = true;
                try {
                    const data = JSON.parse(this.responseText);
                    console.log("[XHR] 接口响应数据:", data);
                    if (data[SUCCESS_FIELD] === SUCCESS_VALUE) {
                        console.log(`[XHR] 响应符合成功条件（${SUCCESS_FIELD}=${SUCCESS_VALUE}），保存缓存`);
                        Cache.save(data);
                    } else {
                        console.warn(`[XHR] 响应不符合成功条件！${SUCCESS_FIELD}=${data[SUCCESS_FIELD]}`);
                    }
                } catch (e) {
                    console.error("[XHR] 解析响应失败（非JSON）:", e);
                }
            });
        }
        return originXhrOpen.apply(this, arguments);
    };

    // 触发查询按钮
    const triggerQuery = () => {
        const selectors = [
            '.u-btn-right .right-btn button.el-button--primary',
            'button:contains("查询")',
            'button[type="button"].el-button--primary',
            '#queryBtn',
            '.search-btn'
        ];
        
        for (const sel of selectors) {
            const btn = document.querySelector(sel);
            if (btn) {
                console.log(`[触发查询] 找到按钮（选择器：${sel}），点击`);
                btn.click();
                return true;
            }
        }
        
        console.error("[触发查询] 未找到查询按钮，请手动点击");
        return false;
    };

    // 数据解析
    const parseData = (rawData) => {
        try {
            // 示例：假设数据格式为 { rows: [{ actName: "活动", items: [{ checkItemTitle: "检查项", rmarkLst: [{ markId: "1", markFlag: "1", checkItemValue: "图片URL" }] }] }] }
            return rawData.rows.flatMap(row => 
                (row.items || []).flatMap(item => 
                    (item.rmarkLst || []).map(mark => ({
                        id: mark.markId || Date.now(),
                        act: row.actName || '未知活动',
                        item: item.checkItemTitle || '未知项',
                        flag: mark.markFlag === '1' ? '合格' : mark.markFlag === '2' ? '不合格' : '未点检',
                        color: mark.markFlag === '1' ? '#67c23a' : mark.markFlag === '2' ? '#f56c6c' : '#909399',
                        img: mark.checkItemValue || '',
                        rawData: mark
                    }))
                )
            ).filter(item => item.img);
        } catch (e) {
            console.error("[数据解析] 格式错误:", e);
            console.log("原始数据:", rawData);
            return [];
        }
    };

    // 1. 优化瀑布流图片墙（动态高度+自适应列数+响应式设计）
    const createImageWall = (items) => {
        if (!items.length) {
            alert('无图片数据');
            return;
        }

        // 创建容器
        const wall = document.createElement('div');
        wall.id = 'inspection-wall';
        wall.className = 'fixed inset-0 bg-black/70 z-[10000] p-2 overflow-auto flex flex-col';
        wall.setAttribute('style', 'transition: opacity 0.3s ease;');

        // 工具栏
        wall.innerHTML = `
            <div class="bg-white p-4 rounded-lg m-2 shadow-md flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">检核图片墙（共${items.length}张）</h3>
                <div class="flex space-x-2">
                    <button id="refresh-wall" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-all shadow-sm flex items-center">
                        <i class="fa fa-refresh mr-1"></i>刷新数据
                    </button>
                    <button id="toggle-fullscreen" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-all shadow-sm flex items-center">
                        <i class="fa fa-expand mr-1"></i>全屏
                    </button>
                    <button id="close-wall" class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-all shadow-sm flex items-center">
                        <i class="fa fa-times mr-1"></i>关闭
                    </button>
                </div>
            </div>
            <div id="wall-content" class="flex-1 m-2 bg-white p-4 rounded-lg overflow-auto">
                <div id="image-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"></div>
            </div>
        `;

        // 添加图片项
        const grid = wall.querySelector('#image-grid');
        items.forEach(item => {
            const card = document.createElement('div');
            card.className = 'inspection-card bg-white rounded-lg overflow-hidden shadow border border-gray-100 transition-all duration-300 hover:shadow-md hover:-translate-y-1';
            
            card.innerHTML = `
                <div class="relative overflow-hidden aspect-[4/3] bg-gray-100 flex items-center justify-center">
                    <img src="${item.img}" alt="${item.act}-${item.item}" class="w-full h-full object-cover transition-transform duration-500 hover:scale-105">
                    <div class="absolute top-2 right-2 bg-${item.color === '#67c23a' ? 'green' : item.color === '#f56c6c' ? 'red' : 'gray'}-500 text-white text-xs font-bold px-2 py-1 rounded shadow-sm">
                        ${item.flag}
                    </div>
                </div>
                <div class="p-3 border-t border-gray-100">
                    <div class="font-semibold text-gray-800 text-sm mb-1 line-clamp-1">${item.act}</div>
                    <div class="text-gray-600 text-xs mb-2 line-clamp-1">${item.item}</div>
                    <div class="flex justify-between items-center">
                        <span class="text-xs font-medium text-${item.color === '#67c23a' ? 'green' : item.color === '#f56c6c' ? 'red' : 'gray'}-500">${item.flag}</span>
                        <button class="update-result px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors" 
                                data-id="${item.id}" 
                                data-flag="${item.rawData.markFlag}"
                                data-index="${items.indexOf(item)}">
                            <i class="fa fa-pencil mr-1"></i>更新结果
                        </button>
                    </div>
                </div>
            `;

            grid.appendChild(card);

            // 点击图片查看大图
            card.querySelector('img').onclick = () => {
                showImageModal(item);
            };
        });

        // 添加到页面
        document.body.appendChild(wall);
        // 添加淡入动画
        setTimeout(() => {
            wall.style.opacity = '1';
        }, 10);

        // 绑定事件
        document.getElementById('close-wall').onclick = () => {
            wall.style.opacity = '0';
            setTimeout(() => wall.remove(), 300);
        };
        
        document.getElementById('refresh-wall').onclick = () => {
            wall.style.opacity = '0';
            setTimeout(() => {
                wall.remove();
                loadAndShowData(true);
            }, 300);
        };
        
        document.getElementById('toggle-fullscreen').onclick = () => {
            const content = document.getElementById('wall-content');
            if (!document.fullscreenElement) {
                content.requestFullscreen().catch(err => {
                    console.error(`全屏请求错误: ${err.message}`);
                });
            } else {
                document.exitFullscreen();
            }
        };

        // 更新结果按钮事件
        document.querySelectorAll('.update-result').forEach(btn => {
            btn.onclick = (e) => {
                const index = parseInt(e.currentTarget.dataset.index);
                const currentFlag = e.currentTarget.dataset.flag;
                
                // 创建模态框
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black/50 z-[10001] flex items-center justify-center p-4';
                modal.setAttribute('style', 'opacity: 0; transition: opacity 0.3s ease;');
                
                modal.innerHTML = `
                    <div class="bg-white rounded-lg p-5 w-full max-w-md shadow-xl transform transition-all duration-300 scale-95">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">更新检核结果</h3>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">当前状态:</label>
                            <div class="flex items-center">
                                <span id="current-status" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mr-2 ${item.color === '#67c23a' ? 'bg-green-100 text-green-800' : item.color === '#f56c6c' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'}">
                                    ${items[index].flag}
                                </span>
                            </div>
                            <select id="new-status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <option value="1">合格</option>
                                <option value="2">不合格</option>
                                <option value="0">未点检</option>
                            </select>
                        </div>
                        <div class="flex justify-end space-x-3">
                            <button id="cancel-update" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                取消
                            </button>
                            <button id="confirm-update" class="px-4 py-2 bg-blue-500 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                确认更新
                            </button>
                        </div>
                    </div>
                `;
                
                document.body.appendChild(modal);
                // 添加淡入动画
                setTimeout(() => {
                    modal.style.opacity = '1';
                    modal.querySelector('div').classList.remove('scale-95');
                    modal.querySelector('div').classList.add('scale-100');
                }, 10);
                
                document.getElementById('new-status').value = currentFlag;
                
                // 关闭模态框
                document.getElementById('cancel-update').onclick = () => {
                    modal.querySelector('div').classList.remove('scale-100');
                    modal.querySelector('div').classList.add('scale-95');
                    modal.style.opacity = '0';
                    setTimeout(() => modal.remove(), 300);
                };
                
                // 确认更新
                document.getElementById('confirm-update').onclick = () => {
                    const newFlag = document.getElementById('new-status').value;
                    
                    // 关闭模态框
                    modal.querySelector('div').classList.remove('scale-100');
                    modal.querySelector('div').classList.add('scale-95');
                    modal.style.opacity = '0';
                    setTimeout(() => modal.remove(), 300);
                    
                    // 实际项目中应替换为真实API调用
                    console.log(`[模拟更新] ID=${items[index].id}，状态=${newFlag}`);
                    
                    // 更新UI
                    const newFlagText = newFlag === '1' ? '合格' : newFlag === '2' ? '不合格' : '未点检';
                    const newFlagColor = newFlag === '1' ? '#67c23a' : newFlag === '2' ? '#f56c6c' : '#909399';
                    
                    items[index].flag = newFlagText;
                    items[index].color = newFlagColor;
                    items[index].rawData.markFlag = newFlag;
                    
                    // 重新渲染
                    const cards = document.querySelectorAll('.inspection-card');
                    const card = cards[index];
                    if (card) {
                        // 更新标签颜色和文本
                        const label = card.querySelector('.absolute.top-2.right-2');
                        label.textContent = newFlagText;
                        label.className = 'absolute top-2 right-2 bg-' + (newFlagColor === '#67c23a' ? 'green' : newFlagColor === '#f56c6c' ? 'red' : 'gray') + '-500 text-white text-xs font-bold px-2 py-1 rounded shadow-sm';
                        
                        // 更新底部状态文本颜色
                        const statusText = card.querySelector('.flex.justify-between.items-center span');
                        statusText.textContent = newFlagText;
                        statusText.className = 'text-xs font-medium text-' + (newFlagColor === '#67c23a' ? 'green' : newFlagColor === '#f56c6c' ? 'red' : 'gray') + '-500';
                        
                        // 更新按钮数据
                        const updateBtn = card.querySelector('.update-result');
                        updateBtn.dataset.flag = newFlag;
                    }
                    
                    // 显示成功提示
                    showToast('更新成功');
                };
            };
        });
    };

    // 2. 图片查看弹框（动态高度+关闭功能+图片懒加载）
    const showImageModal = (item) => {
        // 创建遮罩层
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black/85 z-[10001] flex items-center justify-center p-4 opacity-0 transition-opacity duration-300';
        
        // 创建弹框内容
        modal.innerHTML = `
            <div class="bg-white rounded-lg shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col transform transition-transform duration-300 scale-95">
                <div class="flex justify-between items-center p-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">${item.act}</h3>
                    <button id="close-modal" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                        <i class="fa fa-times text-xl"></i>
                    </button>
                </div>
                <div class="flex-1 overflow-hidden relative">
                    <div class="absolute inset-0 flex items-center justify-center bg-gray-100">
                        <img id="modal-image" src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSI+PHBhdGggZD0iTTEyIDJDOC45MyAyIDYgNC45MyA2IDhDNiAxMS4wNyA4LjkzIDE0IDEyIDE0QzE1LjA3IDE0IDE4IDExLjA3IDE4IDhDMTggNC45MyAxNS4wNyAyIDEyIDJNMTEgMTdIMTRWMTlIMTFWMTdNNyA1QzcuNTUgNSA4IDUuNDUgOCA2QzggNi41NSA3LjU1IDcgNyA3QzYuNDUgNyA2IDYuNTUgNiA2QzYgNS40NSA2LjQ1IDUgNyA1WiIgZmlsbD0iIzZBNkE2QSIgZmlsbC1vcGFjaXR5PSIwLjgiLz48L3N2Zz4=" alt="${item.act}-${item.item}" class="max-w-full max-h-full object-contain opacity-0 transition-opacity duration-500">
                    </div>
                </div>
                <div class="p-4 border-t border-gray-200">
                    <div class="text-sm text-gray-600 mb-3">${item.item}</div>
                    <div class="flex justify-between items-center">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${item.color === '#67c23a' ? 'bg-green-100 text-green-800' : item.color === '#f56c6c' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'}">
                            检核结果: ${item.flag}
                        </span>
                        <button class="update-result-modal px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors" 
                                data-id="${item.id}" 
                                data-flag="${item.rawData.markFlag}">
                            <i class="fa fa-pencil mr-1"></i>更新结果
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // 添加动画效果
        setTimeout(() => {
            modal.classList.remove('opacity-0');
            modal.querySelector('div').classList.remove('scale-95');
            modal.querySelector('div').classList.add('scale-100');
        }, 10);
        
        // 图片加载
        const img = modal.querySelector('#modal-image');
        img.onload = () => {
            img.classList.remove('opacity-0');
        };
        img.src = item.img;
        
        // 关闭按钮事件
        modal.querySelector('#close-modal').onclick = () => {
            modal.classList.add('opacity-0');
            modal.querySelector('div').classList.remove('scale-100');
            modal.querySelector('div').classList.add('scale-95');
            setTimeout(() => modal.remove(), 300);
        };
        
        // 点击遮罩层关闭
        modal.onclick = (e) => {
            if (e.target === modal) {
                modal.querySelector('#close-modal').click();
            }
        };
        
        // 更新结果按钮事件
        modal.querySelector('.update-result-modal').onclick = (e) => {
            const id = e.currentTarget.dataset.flag;
            const currentFlag = e.currentTarget.dataset.flag;
            
            // 创建更新模态框
            const updateModal = document.createElement('div');
            updateModal.className = 'fixed inset-0 bg-black/50 z-[10002] flex items-center justify-center p-4 opacity-0 transition-opacity duration-300';
            
            updateModal.innerHTML = `
                <div class="bg-white rounded-lg p-5 w-full max-w-md shadow-xl transform transition-all duration-300 scale-95">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">更新检核结果</h3>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">当前状态:</label>
                        <div class="flex items-center">
                            <span id="current-status" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mr-2 ${item.color === '#67c23a' ? 'bg-green-100 text-green-800' : item.color === '#f56c6c' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'}">
                                ${item.flag}
                            </span>
                        </div>
                        <select id="new-status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <option value="1">合格</option>
                            <option value="2">不合格</option>
                            <option value="0">未点检</option>
                        </select>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button id="cancel-update" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button id="confirm-update" class="px-4 py-2 bg-blue-500 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            确认更新
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(updateModal);
            
            // 添加动画效果
            setTimeout(() => {
                updateModal.classList.remove('opacity-0');
                updateModal.querySelector('div').classList.remove('scale-95');
                updateModal.querySelector('div').classList.add('scale-100');
            }, 10);
            
            document.getElementById('new-status').value = currentFlag;
            
            // 关闭模态框
            document.getElementById('cancel-update').onclick = () => {
                updateModal.classList.add('opacity-0');
                updateModal.querySelector('div').classList.remove('scale-100');
                updateModal.querySelector('div').classList.add('scale-95');
                setTimeout(() => updateModal.remove(), 300);
            };
            
            // 确认更新
            document.getElementById('confirm-update').onclick = () => {
                const newFlag = document.getElementById('new-status').value;
                
                // 关闭模态框
                updateModal.classList.add('opacity-0');
                updateModal.querySelector('div').classList.remove('scale-100');
                updateModal.querySelector('div').classList.add('scale-95');
                setTimeout(() => {
                    updateModal.remove();
                    modal.querySelector('#close-modal').click();
                }, 300);
                
                // 实际项目中应替换为真实API调用
                console.log(`[模拟更新] ID=${id}，状态=${newFlag}`);
                
                // 更新UI
                loadAndShowData(true);
                
                // 显示成功提示
                setTimeout(() => {
                    showToast('更新成功');
                }, 400);
            };
        };
    };

    // 3. 轻量级提示框
    const showToast = (message, duration = 2000) => {
        const toast = document.createElement('div');
        toast.className = 'fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-black/80 text-white px-4 py-2 rounded-lg shadow-lg z-[10003] opacity-0 transition-opacity duration-300 flex items-center';
        toast.innerHTML = `
            <i class="fa fa-check-circle mr-2"></i>
            <span>${message}</span>
        `;
        
        document.body.appendChild(toast);
        
        // 显示提示框
        setTimeout(() => {
            toast.classList.remove('opacity-0');
        }, 10);
        
        // 自动隐藏
        setTimeout(() => {
            toast.classList.add('opacity-0');
            setTimeout(() => toast.remove(), 300);
        }, duration);
    };

    // 4. 数据加载与展示主流程
    const loadAndShowData = async (forceRefresh = false) => {
        console.log("[主流程] 开始加载数据...");
        
        // 清除旧图片墙
        const oldWall = document.getElementById('inspection-wall');
        if (oldWall) {
            oldWall.style.opacity = '0';
            setTimeout(() => oldWall.remove(), 300);
        }
        
        // 读取缓存
        let data = Cache.get();
        if (data && !forceRefresh) {
            console.log("[主流程] 使用缓存数据");
            const items = parseData(data);
            if (items.length) {
                createImageWall(items);
            } else {
                showToast('解析后无有效图片数据');
            }
            return;
        }
        
        // 触发查询
        const triggered = triggerQuery();
        if (!triggered) {
            showToast("请先手动点击页面上的“查询”按钮，再重试");
            return;
        }
        
        // 显示加载中提示
        const loadingToast = document.createElement('div');
        loadingToast.className = 'fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-black/80 text-white px-4 py-2 rounded-lg shadow-lg z-[10003] flex items-center';
        loadingToast.innerHTML = `
            <i class="fa fa-spinner fa-spin mr-2"></i>
            <span>正在加载数据...</span>
        `;
        document.body.appendChild(loadingToast);
        
        // 等待响应
        console.log("[主流程] 等待接口响应（8秒）...");
        await new Promise(resolve => setTimeout(resolve, 8000));
        
        // 移除加载提示
        loadingToast.remove();
        
        // 检查是否有响应
        if (!responseReceived) {
            console.error("[主流程] 8秒内未收到接口响应！");
            showToast("数据加载失败：未收到接口响应");
            return;
        }
        
        // 再次检查缓存
        data = Cache.get();
        if (data) {
            console.log("[主流程] 接口响应成功，使用缓存数据");
            const items = parseData(data);
            if (items.length) {
                createImageWall(items);
            } else {
                showToast('解析后无有效图片数据');
            }
        } else {
            console.error("[主流程] 接口响应了，但未保存到缓存");
            showToast("数据加载失败：缓存中无数据");
        }
    };

   // 5. 创建主按钮
const createMainButton = () => {
    if (document.getElementById('inspect-main-btn')) return;
    const btn = document.createElement('button');
    btn.id = 'inspect-main-btn';
    btn.innerHTML = '<i class="fa fa-th-large mr-1"></i>检核瀑布流';
    // 修改这里的样式，使用标准的CSS属性
    btn.className = 'fixed top-[80px] right-[20px] z-[9998] px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-all shadow-md flex items-center';
    btn.style.top = '80px'; // 添加标准的top属性
    btn.style.right = '20px'; // 添加标准的right属性
    btn.onclick = loadAndShowData;
    document.body.appendChild(btn);
};

    // 6. 初始化
    createMainButton();
    console.log("=== 检核瀑布流工具就绪 ===");
    console.log("点击右上角按钮查看瀑布流图片");
    
    // 提供调试接口
    window.inspectionTool = {
        loadData: () => loadAndShowData(true),
        clearCache: () => {
            Cache.clear();
            showToast("缓存已清除");
        },
        showData: () => {
            const data = Cache.get();
            if (data) {
                console.log("当前缓存数据:", data);
                const items = parseData(data);
                if (items.length) {
                    createImageWall(items);
                } else {
                    showToast('解析后无有效图片数据');
                }
            } else {
                showToast("缓存中无数据，请先加载");
            }
        }
    };
})();